<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_stock_return_picking_form_inherit_helpdesk_stock_ni" model="ir.ui.view">
        <field name="name">stock.return.picking.stock.helpdesk.return.repair.return.exchange</field>
        <field name="inherit_id" ref="helpdesk_stock.view_stock_return_picking_form_inherit_helpdesk_stock"/>
        <field name="model">stock.return.picking</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='ticket_id']" position="after">
                <field name="ticket_type" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='sale_order_id']" position="attributes">
                <attribute name="readonly">ticket_id</attribute>
            </xpath>
            <xpath expr="//group/group/field[@name='picking_id']" position="attributes">
                <attribute name="readonly">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_stock_return_picking_form_inherit_helpdesk_ni" model="ir.ui.view">
        <field name="name">stock.return.picking.stock.helpdesk.return.form</field>
        <field name="inherit_id" ref="stock.view_stock_return_picking_form"/>
        <field name="priority">30</field>
        <field name="model">stock.return.picking</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_return_moves']/list/field[@name='product_id']" position="attributes">
                <attribute name="readonly">wizard_id.ticket_id</attribute>
            </xpath>
            <xpath expr="//field[@name='product_return_moves']/list/field[@name='product_id']" position="after">
                <field name="lot_ids" widget="many2many_tags" readonly="1" force_save="1"/>
            </xpath>
            <xpath expr="//field[@name='product_return_moves']/list/field[@name='product_id']" position="before">
                <field name="part_code_id" readonly="1"/>
            </xpath>
            <xpath expr="//button[@name='action_create_returns']" position="attributes">
                <attribute name="invisible">ticket_id</attribute>
            </xpath>
            <xpath expr="//button[@name='action_create_returns_all']" position="attributes">
                <attribute name="invisible">ticket_id</attribute>
            </xpath>
            <xpath expr="//field[@name='rma_reason_id']" position="attributes">
                <attribute name="invisible">ticket_id</attribute>
            </xpath>
            <xpath expr="//button[@name='action_create_exchanges']" position="attributes">
                <attribute name="invisible">ticket_id</attribute>
            </xpath>
            <xpath expr="//button[@name='action_create_exchanges']" position="after">
                <field name="ticket_type" invisible="1"/>
            <button name="action_create_repair" string="Return for Repair"
                    invisible="not ticket_id or ticket_type!='repair'" type="object" class="btn-primary"/>
            <button name="action_create_exchanges" string="Return for Exchange"
                    invisible="not ticket_id or ticket_type!='exchange'" type="object" class="btn-primary"/>
            <button name="action_create_returns" string="Return"
                    invisible="not ticket_id or ticket_type!='credit_note'" type="object" class="btn-primary"/>
            </xpath>
        </field>
    </record>
</odoo>