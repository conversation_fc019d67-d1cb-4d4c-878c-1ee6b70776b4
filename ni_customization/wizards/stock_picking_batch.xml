<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="action_consolidate_deliveries" model="ir.actions.server">
            <field name="name">Consolidate Deliveries</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
                if records:
                    records.consolidate_deliveries()
            </field>
        </record>

        <record id="stock_picking_batch.stock_add_to_wave_action_stock_picking" model="ir.actions.act_window">
            <field name="name">Consolidate Deliveries</field>
            <field name="domain">[('picking_type_id.code', '=', 'outgoing')]</field>
        </record>
    </data>
</odoo>