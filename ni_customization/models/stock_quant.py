from odoo import models, fields, api


class StockQuant(models.Model):
    _inherit = 'stock.quant'

    part_code_id = fields.Many2one(related='product_id.part_code_id', store=True)
    brand_id = fields.Many2one(related='product_id.brand_id', store=True)
    barcode = fields.Char(related='product_id.barcode', store=True)
    serial = fields.Char(related='product_id.barcode', store=True)
    is_returnable = fields.Selection(related='product_id.is_returnable', store=True)
    is_warranty_product = fields.Boolean(related='product_id.is_warranty_product', store=True)
    is_licence_tracked = fields.Selection(related='product_id.is_licence_tracked', store=True)
    is_amc_tracked = fields.Selection(related='product_id.is_amc_tracked', store=True)
    hs_code = fields.Char(related='product_id.hs_code', store=True)
    volume = fields.Float(related='product_id.volume', store=True)
    weight = fields.Float(related='product_id.weight', store=True)

    @api.depends('location_id', 'lot_id', 'package_id', 'owner_id')
    def _compute_display_name(self):
        """name that will be displayed in the detailed operation"""
        for record in self:
            # name = [record.location_id.display_name]
            name = []
            if record.lot_id:
                name.append(record.lot_id.name)
            else:
                name = [record.location_id.display_name]
            if record.package_id:
                name.append(record.package_id.name)
            if record.owner_id:
                name.append(record.owner_id.name)
            record.display_name = ' - '.join(name)

    @api.model
    def web_search_read(self, domain, specification, offset=0, limit=None, order=None, count_limit=None):
        print(self.env.context)
        print(domain)
        return super(StockQuant, self).web_search_read(domain, specification, offset, limit, order,count_limit)
