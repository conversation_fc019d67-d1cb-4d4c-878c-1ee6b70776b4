from odoo import api, Command, fields, models, _
from odoo.exceptions import UserError, ValidationError


class ReturnPicking(models.TransientModel):
    _inherit = 'stock.return.picking'

    rma_reason_id = fields.Many2one('rma.reason', string="RMA Reason", required=True)

    def action_create_returns(self):
        res = super().action_create_returns()
        picking_id = self.env['stock.picking'].browse(res['res_id'])
        if self.rma_reason_id and picking_id.picking_type_id.code == 'incoming':
            picking_id.rma_reason_id = self.rma_reason_id.id
            picking_id.so_id = picking_id.sale_id.id
        return res
