<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data auto_sequence="1">
    <record id="mod_390" model="account.report">
        <field name="name">Tax Report (Mod 390)</field>
        <field name="sequence">390</field>
        <field name="filter_analytic" eval="False"/>
        <field name="filter_date_range" eval="True"/>
        <field name="filter_period_comparison" eval="False"/>
        <field name="filter_journals" eval="True"/>
        <field name="country_id" ref="base.es"/>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="section_report_ids" eval="[Command.set([ref('mod_390_section_1'),ref('mod_390_section_2'),ref('mod_390_section_3'),ref('mod_390_section_4'),ref('mod_390_section_5'),ref('mod_390_section_6'),ref('mod_390_section_7')])]"/>
    </record>
</data>
</odoo>
