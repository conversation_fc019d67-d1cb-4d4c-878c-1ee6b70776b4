<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <template id="report_packing_list">
            <t t-call="web.basic_layout">
                <t t-set="o" t-value="o.with_context(lang=lang)"/>
                    <div style="width:100%; height: 10px;"/>
                        <div class="row d-flex align-items-center" style="border: 1px solid black;">
                            <style>
                                p {
                                margin: 0px;
                                }
                            </style>
                            <div class="col-6 ps-5" name="company_address">
                                <img t-if="o.company_id.logo" class="o_company_logo_small" t-att-src="image_data_uri(o.company_id.logo)" alt="Logo"/>
                            </div>
                            <div class="col-6">
                                <ul class="list-unstyled text-end" style="padding: 3px;" name="company_address_list">
                                    <li t-if="o.company_id.is_company_details_empty"><span t-field="o.company_id.partner_id" t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": true}'>
                                        <div class="bg-light border-1 rounded h-100 d-flex flex-column align-items-center justify-content-center p-4 w-100 opacity-75 text-muted text-center">
                                            <strong>Company address block</strong>
                                            <div>Contains the company address.</div>
                                        </div>
                                    </span></li>
                                    <li t-else="">
                                        <span t-field="o.company_id.company_details">
                                            <div class="bg-light border-1 rounded h-100 d-flex flex-column align-items-center justify-content-center p-4 w-100 opacity-75 text-muted text-center">
                                                <strong>Company details block</strong>
                                                <div>Contains the company details.</div>
                                            </div>
                                        </span>
                                    </li>
                                    <li>
                                        TRN <span t-esc="o.company_id.vat">**********</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="text-center border-start border-end" style="background: #d9e1f2;">
                            <span style="font-size: 30px;vertical-align:middle;">PACKING LIST</span>
                        </div>
                        <table style="width: 100%" class="fw-bold">
                            <thead>
                                <tr style="background: #d9e1f2;">
                                    <td class="text-center" style="width:20%;vertical-align:middle;">Date</td>
                                    <td class="text-center" style="width:20%;vertical-align:middle;">Terms of Sale</td>
                                    <td class="text-center" style="width:30%;vertical-align:middle;">Reference</td>
                                    <td class="text-center" style="width:15%;vertical-align:middle;">TRN NO</td>
                                    <td class="text-center" style="width:15%;vertical-align:middle;">Currency</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-center" style="vertical-align:middle;"><t t-esc="o.scheduled_date.date().strftime('%d-%b-%Y')"/></td>
                                    <td class="text-center" style="vertical-align:middle;"><t t-esc="o.sale_id.incoterm.name"/></td>
                                    <td class="text-center" style="vertical-align:middle;"><t t-esc="o.sale_id.name"/></td>
                                    <td class="text-center" style="vertical-align:middle;"><t t-esc="o.partner_id.vat"/></td>
                                    <td class="text-center" style="vertical-align:middle;"><t t-esc="o.partner_id.currency_id.name"/></td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="row">
                            <table style="border: none;width: 40%;" class="fw-bold">
                                <tr style="border: none;">
                                    <td class="px-1" style="background: #d9e1f2;border: none;width: 25%;vertical-align:middle;">Shipper :</td>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="o.sale_id.user_id.name"/></td>
                                </tr>
                            </table>
                            <table class="col-7 fw-bold" style="border: none;width: 60%;">
                                <tr style="border: none;">
                                    <td class="px-1" style="background: #d9e1f2;border: none;width: 30%;vertical-align:middle;">Consignee :</td>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="o.partner_id.name"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="row">
                            <t t-set="shipper_add" t-value="o.picking_type_id.warehouse_id.partner_id"/>
                            <table class="col-5" style="border: none;width: 40%;">
                                <t t-set="rendered_lines_ship" t-value="0"/>
                                <tr style="border: none;">
                                    <t t-set="rendered_lines_ship" t-value="rendered_lines_ship + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="shipper_add.name"/> </td>
                                </tr>
                                <tr style="border: none;" t-if="shipper_add.street">
                                    <t t-set="rendered_lines_ship" t-value="rendered_lines_ship + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="shipper_add.street"/></td>
                                </tr>
                                <tr style="border: none;" t-if="shipper_add.street2">
                                    <t t-set="rendered_lines_ship" t-value="rendered_lines_ship + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="shipper_add.street2"/></td>
                                </tr>
                                <tr style="border: none;" t-if="shipper_add.city or shipper_add.state_id or shipper_add.zip">
                                    <t t-set="rendered_lines_ship" t-value="rendered_lines_ship + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;">
                                        <t t-if="shipper_add.city"><t t-esc="shipper_add.city"/>, </t>
                                        <t t-if="shipper_add.state_id"><t t-esc="shipper_add.state_id.name"/>, </t>
                                        <t t-if="shipper_add.zip"><t t-esc="shipper_add.zip"/></t>
                                    </td>
                                </tr>
                                <tr style="border: none;" t-if="shipper_add.country_id">
                                    <t t-set="rendered_lines_ship" t-value="rendered_lines_ship + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="shipper_add.country_id.name"/></td>
                                </tr>
                                <tr style="border: none;" t-if="shipper_add.phone">
                                    <t t-set="rendered_lines_ship" t-value="rendered_lines_ship + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;">Tel: <t t-esc="shipper_add.phone"/></td>
                                </tr>
                                <t t-foreach="range(6 - rendered_lines_ship)" t-as="i">
                                    <tr style="border: none;">
                                        <td class="px-1" style="border: none;vertical-align:middle;">&#8203;</td>
                                    </tr>
                                </t>
                            </table>
                            <table class="col-7" style="border: none;width: 60%;">
                                <t t-set="rendered_lines_cons" t-value="0"/>
                                <tr style="border: none;">
                                    <t t-set="rendered_lines_cons" t-value="rendered_lines_cons + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="o.partner_id.name"/></td>
                                </tr>
                                <tr style="border: none;" t-if="o.partner_id.street">
                                    <t t-set="rendered_lines_cons" t-value="rendered_lines_cons + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="o.partner_id.street"/></td>
                                </tr>
                                <tr style="border: none;" t-if="o.partner_id.street2">
                                    <t t-set="rendered_lines_cons" t-value="rendered_lines_cons + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="o.partner_id.street2"/></td>
                                </tr>
                                <tr style="border: none;" t-if="o.partner_id.city or o.partner_id.state_id or o.partner_id.zip">
                                    <t t-set="rendered_lines_cons" t-value="rendered_lines_cons + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;">
                                        <t t-if="o.partner_id.city"><t t-esc="o.partner_id.city"/>, </t>
                                        <t t-if="o.partner_id.state_id"><t t-esc="o.partner_id.state_id.name"/>, </t>
                                        <t t-if="o.partner_id.zip"><t t-esc="o.partner_id.zip"/></t>
                                    </td>
                                </tr>
                                <tr style="border: none;" t-if="o.partner_id.country_id">
                                    <t t-set="rendered_lines_cons" t-value="rendered_lines_cons + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;"><t t-esc="o.partner_id.country_id.name"/></td>
                                </tr>
                                <tr style="border: none;" t-if="o.partner_id.phone">
                                    <t t-set="rendered_lines_cons" t-value="rendered_lines_cons + 1"/>
                                    <td class="px-1" style="border: none;vertical-align:middle;">Tel: <t t-esc="o.partner_id.phone"/></td>
                                </tr>
                                <t t-foreach="range(6 - rendered_lines_cons)" t-as="i">
                                    <tr style="border: none;">
                                        <td class="px-1" style="border: none;vertical-align:middle;">&#8203;</td>
                                    </tr>
                                </t>
                            </table>
                        </div>
                        <div>
                            <table style="width:100%;">
                                <tr class="fw-bold">
                                    <td class="px-1" style="background: #d9e1f2;width: 40%;">COUNTRY OF ULTIMATE DESTINATION</td>
                                    <td class="px-1 text-center" style="width:60%" colspan="4"><t t-esc="o.partner_id.country_id.name"/></td>
                                </tr>
                                <tr class="fw-bold">
                                    <td class="px-1" style="background: #d9e1f2;width: 40%;">CARGO NAME</td>
                                    <td class="px-1 text-center" style="width:60%" colspan="4"><t t-esc="o.carrier_id.name"/></td>
                                </tr>
                                <tr class="fw-bold">
                                    <td class="px-1" style="background: #d9e1f2;width: 40%;">INTERNATIONAL AIRWAYBILL NUMBER</td>
                                    <td class="px-1 text-center" style="width:60%" colspan="4"><t t-esc="o.carrier_tracking_ref"/></td>
                                </tr>
<!--                                <tr>-->
<!--                                    <td class="px-1 text-center" style="background: #d9e1f2;width: 40%;">HS CODE</td>-->
<!--                                    <td class="px-1 text-center" style="width:60%" colspan="4"><t t-esc="o._get_hs_codes()"/></td>-->
<!--                                </tr>-->
                                <tr class="fw-bold">
                                    <td class="px-1 text-center" style="background: #d9e1f2;width: 40%;">DESCRIPTION OF GOODS</td>
                                    <td class="px-1 text-center" style="width:20%;background: #d9e1f2;">PART CODE</td>
                                    <td class="px-1 text-center" style="width:15%;background: #d9e1f2;">HS CODE</td>
                                    <td class="px-1 text-center" style="width:5%;background: #d9e1f2;">QTY</td>
                                    <td t-if="not o.show_serial_number" class="px-1 text-center" style="width:20%;background: #d9e1f2;"/>
                                    <td t-if="o.show_serial_number" class="px-1 text-center" style="width:20%;background: #d9e1f2;">SERIAL NO.</td>
                                </tr>
                                <t t-set="sub_total" t-value="0"/>
                                <t t-foreach="o.move_ids_without_package" t-as="l">
                                    <tr>
                                        <td class="px-1 text-center" style="width: 40%;"><t t-esc="l.product_id.name"/></td>
                                        <td class="px-1 text-center" style="width:20%;"><t t-esc="l.part_code_id.name"/></td>
                                        <td class="px-1 text-center" style="width:15%;"><t t-esc="l.product_id.hs_code"/></td>
                                        <td class="px-1 text-center" style="width:5%;"><t t-esc="int(l.quantity)"/></td>
                                        <td t-if="not o.show_serial_number" class="px-1 text-center" style="width:20%;"/>
                                        <td t-if="o.show_serial_number" class="px-1 text-center" style="width:20%;"><t t-esc="l._get_serial_no()"/></td>
                                    </tr>
                                </t>
                                <tr>
                                    <td class="px-1 text-center" style="width: 40%;border-right:0px;"/>
                                    <td class="px-1 text-center" style="width: 25%;border-left: 0px;">SUB-TOTAL</td>
                                    <td class="px-1 text-center" style="width:5%;"/>
                                    <td class="px-1 text-center" style="width:15%;"/>
                                    <td class="px-1 text-center" style="width:15%;"/>
                                </tr>
                                <t t-set="coo" t-value="o._get_countries_of_origins()"/>
                                <tr t-if="coo">
                                    <td class="px-1 text-end" style="width: 100%;" colspan="5">COO - <t t-esc="coo"/> </td>
                                </tr>
                            </table>
                            <table style="width:100%;">
                                <thead>
                                    <tr class="fw-bold">
                                        <td class="px-1 text-center" style="background: #d9e1f2;">DIMENSIONS cms</td>
                                        <td class="px-1 text-center" style="background: #d9e1f2;">NO.OF PACKAGES</td>
                                        <td class="px-1 text-center" style="background: #d9e1f2;">WEIGHT / BOX (Kg)</td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-set="total_packages" t-value="0"/>
                                    <t t-set="total_weight" t-value="0"/>
                                    <t t-foreach="o.dimension_ids" t-as="dl">
                                        <tr>
                                            <td class="px-1 text-center"><t t-esc="dl.length"/>x<t t-esc="dl.width"/>x<t t-esc="dl.height"/> cm</td>
                                            <td class="px-1 text-center"><t t-esc="dl.no_package"/></td>
                                            <td class="px-1 text-center"><t t-esc="dl.weight"/></td>
                                        </tr>
                                        <t t-set="total_packages" t-value="total_packages + dl.no_package"/>
                                        <t t-set="total_weight" t-value="total_weight + dl.weight * dl.no_package"/>
                                    </t>
                                    <tr class="fw-bold">
                                        <td class="px-1" style="background: #d9e1f2;">Total</td>
                                        <td class="px-1 text-center"><t t-esc="total_packages"/> </td>
                                        <td class="px-1 text-center"><t t-esc="total_weight"/> kg</td>
                                    </tr>
                                    <tr class="fw-bold">
                                        <td class="px-1" style="background: #d9e1f2;" colspan="3">Terms and Conditions</td>
                                    </tr>
                                    <tr style=" border: none;">
                                        <td class="p-1" style="font-size: 10px; border: none;" colspan="3">Any goods sold out will remain the property of NETWORKISE CLOUD TECHNOLOGIES LLC until full payment is received.</td>
                                    </tr>
                                    <tr style=" border: none;">
                                        <td class="p-1" style="font-size: 10px; border: none;" colspan="3">Payment has to be settled as per agreed Terms and Conditions, else interest will be applicable after the due date</td>
                                    </tr>
                                    <tr style=" border: none;">
                                        <td class="p-1" style="font-size: 10px; border: none;" colspan="3">Claims by Buyer for shortages or errors in delivery must be made within five (5) days after the delivery of the Goods</td>
                                    </tr>
                                    <tr style=" border: none;">
                                        <td class="p-1" style="font-size: 10px; border: none;" colspan="3">Restocking fees of 25% will be applicable in case of Order cancellation.</td>
                                    </tr>
                                    <tr style=" border: none;">
                                        <td class="p-1" style="font-size: 10px; border: none;" colspan="3">Any Product returned for replacement should be in Original packaging with accessories if any else cannot be entertained. </td>
                                    </tr>
                                    <tr style=" border: none;">
                                        <td style="font-size: 16px;color: #8498b8; border: none;" class="p-1 text-center" colspan="3">Thanks for making us your Business Partner </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
            </t>
        </template>

        <template id="report_packing_list_nwi_base">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-set="lang" t-value="o.partner_id.lang"/>
                    <t t-call="ni_printout_report.report_packing_list"
                       t-lang="lang"/>
                </t>
            </t>
        </template>

        <record id="action_report_packing_list" model="ir.actions.report">
            <field name="name">Packing List</field>
            <field name="model">stock.picking</field>
            <field name="report_type">qweb-pdf</field>
            <field name="paperformat_id" ref="ni_printout_report.paperformat_ni_custom"/>
            <field name="report_name">ni_printout_report.report_packing_list_nwi_base</field>
            <field name="report_file">ni_printout_report.report_packing_list_nwi_base</field>
            <field name="print_report_name">'Packing List ' + object.name</field>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_type">report</field>
            <field name="domain" eval="[('picking_type_code', '=', 'outgoing')]"/>
        </record>

    </data>
</odoo>