<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data noupdate="1">
        <record id="employment_hero_sync_cron" model="ir.cron">
            <field name="name">Employment Hero Payroll Sync</field>
            <field name="model_id" ref="base.model_res_company"/>
            <field name="state">code</field>
            <field name="code">model._eh_payroll_cron_fetch_payrun()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">weeks</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(minutes=5)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo>
