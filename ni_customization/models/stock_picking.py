from odoo import fields, models, api, _
from ast import literal_eval
from odoo.osv import expression
from odoo.exceptions import UserError


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    mode_of_transport = fields.Selection([
        ('road', 'By Road'),
        ('air', 'By Air'),
        ('rail', 'By Rail'),
        ('ship', 'By Ship'),
        ('other', 'Other'),
    ], string='Mode of Transport')

    receipt_state = fields.Selection([
        ('draft', 'Draft'),
        ('inbound', 'Inbound'),
        ('grn', 'Goods Received'),
        ('cancel', 'Cancelled')
    ], string='Receipt Stage', compute='_compute_receipt_state', store=True)

    delivery_state = fields.Selection([
        ('draft', 'Draft'),
        ('waiting', 'Waiting Another Operation'),
        ('confirmed', 'Waiting'),
        ('outbound', 'Ready for Outbound'),
        ('delivery_note', 'Delivery In Progress'),
        ('delivered', 'Delivered'),
        ('cancel', 'Cancelled')
    ], string='Delivery Stage', compute='_compute_delivery_state', store=True)

    sale_enquiry_id = fields.Many2one(related='sale_id.enquiry_id', store=True)
    purchase_enquiry_id = fields.Many2one(related='purchase_id.enquiry_id', store=True)
    so_id = fields.Many2one(related='purchase_id.sale_order_id', store=True)
    rma_reason_id = fields.Many2one('rma.reason', string="RMA Reason")
    backorder_id = fields.Many2one(string="Pending Delivery of")
    show_serial_number = fields.Boolean(string='Show Serial Numbers')

    @api.model
    def get_action_picking_tree_incoming(self, pending_receipt=False):
        if pending_receipt:
            action = self._get_action('stock.action_picking_tree_incoming')
            action["domain"] = expression.AND([
                literal_eval(action["domain"] or '[]'), [('receipt_state', 'in', ['draft', 'inbound'])]
            ])
            return action
        return self._get_action('stock.action_picking_tree_incoming')

    @api.model
    def get_action_picking_tree_outgoing(self, pending_delivery=False):
        if pending_delivery:
            action = self._get_action('stock.action_picking_tree_outgoing')
            action["domain"] = expression.AND([
                literal_eval(action["domain"] or '[]'), [('delivery_state', 'not in', ['delivered', 'cancel'])]
            ])
            return action
        return self._get_action('stock.action_picking_tree_outgoing')

    @api.depends('state')
    def _compute_receipt_state(self):
        for picking in self:
            if picking.state == 'draft':
                picking.receipt_state = 'draft'
            elif picking.state == 'assigned':
                picking.receipt_state = 'inbound'
            elif picking.state == 'done':
                picking.receipt_state = 'grn'
            elif picking.state == 'cancel':
                picking.receipt_state = 'cancel'

    @api.depends('state')
    def _compute_delivery_state(self):
        for delivery in self:
            if delivery.state == 'draft':
                delivery.delivery_state = 'draft'
            elif delivery.state == 'waiting':
                delivery.delivery_state = 'waiting'
            elif delivery.state == 'confirmed':
                delivery.delivery_state = 'confirmed'
            elif delivery.state == 'assigned':
                delivery.delivery_state = 'outbound'
            elif delivery.state == 'done':
                delivery.delivery_state = 'delivery_note'
            elif delivery.state == 'cancel':
                delivery.delivery_state = 'cancel'

    def button_validate(self):
        res = super(StockPicking, self).button_validate()
        for picking in self:
            if picking.picking_type_id.code == 'incoming':
                purchase = picking.purchase_id.sudo()
                if purchase and purchase.enquiry_id:
                    enquiry = purchase.enquiry_id
                    all_pos = enquiry.purchase_order_ids.filtered(lambda p: p.state not in ['cancel'])
                    statuses = all_pos.mapped('receipt_status')
                    if all(s == 'full' for s in statuses):
                        enquiry.state = 'grn'
                    else:
                        enquiry.state = 'inbound'
            if picking.picking_type_id.code == 'outgoing':
                sale = picking.sale_id.sudo()
                if sale and sale.enquiry_id:
                    enquiry = sale.enquiry_id
                    if sale.delivery_status == 'full':
                        enquiry.state = 'delivery_note'
                    else:
                        enquiry.state = 'outbound'
                if picking.rma_reason_id:
                    # Find backorder related to this picking
                    if picking.backorder_id:
                        # Get the original sales order
                        sale_order = picking.sale_id
                        if sale_order:
                            # Find pending deliveries related to the same sales order
                            pending_deliveries = self.env['stock.picking'].search([
                                ('sale_id', '=', sale_order.id),
                                ('state', 'not in', ['done', 'cancel']),
                                ('id', '!=', picking.id)
                            ])

                            if pending_deliveries:
                                for delivery in pending_deliveries:
                                    for move in picking.move_ids:
                                        product = move.product_id
                                        part_code_id = move.part_code_id.id if move.part_code_id else False
                                        product_condition_id = move.product_condition_id.id if move.product_condition_id else False

                                        # Check if product already exists in the pending delivery
                                        existing_move = delivery.move_ids.filtered(lambda
                                                                                       m: m.product_id.id == product.id and m.part_code_id.id == part_code_id and m.product_condition_id.id == product_condition_id)

                                        if existing_move:
                                            # Update quantity if move already exists
                                            for existing in existing_move:
                                                existing.product_uom_qty += move.quantity
                                        else:
                                            # Create new move if product doesn't exist in delivery
                                            vals = {
                                                'name': product.name,
                                                'part_code_id': part_code_id,
                                                'product_id': product.id,
                                                'product_condition_id': product_condition_id,
                                                'product_uom': product.uom_id.id,
                                                'product_uom_qty': move.quantity,
                                                'picking_id': delivery.id,
                                                'location_id': delivery.location_id.id,
                                                'location_dest_id': delivery.location_dest_id.id,
                                            }
                                            self.env['stock.move'].create(vals)
        return res

    def write(self, vals):
        if vals.get('mode_of_transport'):
            for picking in self:
                if not self.mode_of_transport:
                    if picking.purchase_enquiry_id and picking.picking_type_id.code == 'incoming':
                        picking.purchase_enquiry_id.state = 'inbound'
                    if picking.sale_enquiry_id and picking.picking_type_id.code == 'outgoing':
                        picking.sale_enquiry_id.state = 'outbound'
        return super(StockPicking, self).write(vals)

    def _action_generate_backorder_wizard(self, show_transfers=False):
        view = self.env.ref('stock.view_backorder_confirmation')
        return {
            'name': _('Create Pending Deliveries?'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'stock.backorder.confirmation',
            'views': [(view.id, 'form')],
            'view_id': view.id,
            'target': 'new',
            'context': dict(self.env.context, default_show_transfers=show_transfers,
                            default_pick_ids=[(4, p.id) for p in self]),
        }

    def button_mark_as_delivered(self):
        for rec in self:
            rec.delivery_state = 'delivered'
            rec.sale_enquiry_id.state = 'delivered'

    def consolidate_deliveries(self):
        # Check if all selected deliveries have the same partner (contact)
        partner_ids = self.mapped('partner_id.id')
        if len(set(partner_ids)) > 1:
            raise UserError(_('All selected deliveries must have the same contact.'))
        # Redirect to the stock.add.to.wave form view
        view = self.env.ref('stock.view_add_to_wave_form')
        return {
            'name': _('Consolidate Deliveries'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'stock.add.to.wave',
            'views': [(view.id, 'form')],
            'view_id': view.id,
            'target': 'new',
            'context': dict(self.env.context, default_picking_ids=[(6, 0, self.ids)]),
        }

    # def action_confirm(self):
    #     if self._context and self._context.get('params', False):
    #         params = self._context.get('params')
    #         if params.get('action') == 'orders':
    #             return True
    #     return super(StockPicking, self).action_confirm()
