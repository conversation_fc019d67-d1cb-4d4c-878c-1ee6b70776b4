# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_social
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: event_social
#. odoo-python
#: code:addons/event_social/models/event_mail.py:0
#: code:addons/event_social/models/event_type_mail.py:0
msgid ""
"As social posts have no recipients, they cannot be triggered by "
"registrations."
msgstr ""

#. module: event_social
#: model:ir.model,name:event_social.model_event_mail
msgid "Event Automated Mailing"
msgstr ""

#. module: event_social
#: model:ir.model,name:event_social.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr ""

#. module: event_social
#: model:ir.model.fields,field_description:event_social.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_social.field_event_type_mail__notification_type
msgid "Send"
msgstr "Nosūtīt"

#. module: event_social
#: model:ir.model.fields.selection,name:event_social.selection__event_mail__notification_type__social_post
#: model:ir.model.fields.selection,name:event_social.selection__event_mail__template_ref__social_post_template
#: model:ir.model.fields.selection,name:event_social.selection__event_type_mail__notification_type__social_post
#: model:ir.model.fields.selection,name:event_social.selection__event_type_mail__template_ref__social_post_template
msgid "Social Post"
msgstr "Sociālais ieraksts"

#. module: event_social
#: model:ir.model,name:event_social.model_social_post_template
msgid "Social Post Template"
msgstr "Sociālā ieraksta veidne"

#. module: event_social
#: model:ir.model.fields,field_description:event_social.field_event_mail__template_ref
#: model:ir.model.fields,field_description:event_social.field_event_type_mail__template_ref
msgid "Template"
msgstr "Sagatave"
