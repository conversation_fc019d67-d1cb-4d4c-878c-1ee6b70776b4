from email.policy import default
from markupsafe import Markup

from odoo import fields, models, api, _
from datetime import datetime
from pytz import timezone
from odoo.exceptions import UserError, ValidationError

SALE_ENQUIRY_STATE = [
    ('new', 'New'),
    ('pending', 'Pending to Quote'),
    ('quoted_to_salesperson', 'Quoted to Sales<PERSON>erson'),
    ('quoted', 'Quoted To Customer'),
    ('pi_shared', 'PI Shared'),
    ('so_generated', 'SO Generated'),
    ('sourcing', 'Sourcing'),
    ('procurement', 'PO Issued'),
    ('inbound', 'Inbound'),
    ('grn', 'Goods Received'),
    ('outbound', 'Ready For Outbound'),
    ('delivery_note', 'Delivery In Progress'),
    ('delivered', 'Delivered'),
    ('executed', "Executed"),
    ('outstanding', "Outstanding Payment"),
    ('lost', 'Lost')
]


class SalesEnquiry(models.Model):
    _name = 'sale.enquiry'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Sales Enquiry'
    _order = 'id desc'

    name = fields.Char(copy=False, default=lambda self: _('New'), string="RFQ ID")
    partner_id = fields.Many2one("res.partner", string="Organization", tracking=True,
                                 domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
    contact_person_id = fields.Many2one('res.partner', string='Contact Person', tracking=True, store=True,
                                        readonly=False, required=True, precompute=True,
                                        compute="_compute_contact_person",
                                        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
    partner_shipping_id = fields.Many2one(
        comodel_name='res.partner',
        string="Delivery Address",
        compute='_compute_partner_shipping_id',
        store=True, readonly=False, required=True, precompute=True, tracking=True)
    state = fields.Selection(SALE_ENQUIRY_STATE, string="Status",
                             readonly=True, copy=False, index=True,
                             tracking=3,
                             default='new')
    enquiry_date = fields.Date('RFQ Date', default=datetime.now().date(), tracking=True)
    contact_number = fields.Char("Contact Number", related="partner_id.phone", store=True)
    email = fields.Char("Email", related="partner_id.email", store=True)
    notes = fields.Html()
    enquiry_line_ids = fields.One2many("sale.enquiry.line", 'enquiry_id')
    pending_user_ids = fields.Many2many('res.users', 'sale_enquiry_pending_users_rel', 'enquiry_id', 'user_id',
                                        string='Pending Users', compute='_compute_pending_user_ids', store=True)
    company_id = fields.Many2one(
        comodel_name='res.company',
        required=True, index=True,
        default=lambda self: self.env.company)
    currency_id = fields.Many2one(
        comodel_name='res.currency',
        ondelete='restrict',
        required=True
    )
    previous_currency_id = fields.Many2one('res.currency', default=lambda self: self.env.company.currency_id)
    show_update_currency_btn = fields.Boolean(default=True)
    enquiry_lost_reason_id = fields.Many2one('enquiry.lost.reason', 'Lost Reason')
    enquiry_lost_feedback = fields.Text(
        'Lost Note'
    )
    purchase_order_ids = fields.Many2many('purchase.order', copy=False)
    rfq_type_id = fields.Many2one('rfq.type', string="Enquiry Source",
                                  default=lambda self: self._get_default_rfq_type())
    user_id = fields.Many2one(
        comodel_name='res.users',
        string="Salesperson",
        default=lambda self: self.env.uid,
        store=True, readonly=False, index=True,
        tracking=2,
        domain=lambda self: "[('groups_id', '=', {}), ('share', '=', False), ('company_ids', '=', company_id)]".format(
            self.env.ref("sales_team.group_sale_salesman").id
        ))
    team_id = fields.Many2one(comodel_name='crm.team')
    team_leader_id = fields.Many2one(
        comodel_name='res.users',
        string="Team Leader",
        compute='_compute_team_leader_id',
        store=True, readonly=True, precompute=True, ondelete="set null",
        change_default=True, check_company=True,  # Unrequired company
        tracking=True,
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
    incoterm_id = fields.Many2one(
        'account.incoterms', 'Incoterm',
        help="International Commercial Terms are a series of predefined commercial terms used in international transactions.")
    incoterm_location = fields.Many2one("res.city", string="Incoterm Location", tracking=True)
    payment_term_id = fields.Many2one(
        comodel_name='account.payment.term',
        string="Payment Terms",
        compute='_compute_payment_term_id',
        store=True, readonly=False, precompute=True, check_company=True,  # Unrequired company
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]", tracking=True)
    enquiry_optional_product_ids = fields.One2many('enquiry.optional.product', 'enquiry_id')
    kit_source_ids = fields.Many2many('kit.source', copy=False)
    kit_source_done_user_ids = fields.Many2many('res.users')
    show_kit_source_button = fields.Boolean(
        compute='_compute_show_kit_source_button',
        string="Show Kit Source Button"
    )
    quotation_ids = fields.Many2many('sale.order', copy=False)
    # Add this field to store procurement users
    procurement_user_ids = fields.Many2many(
        'res.users', string='Procurement Users',
        relation='sale_enquiry_procurement_users_rel',
        column1='enquiry_id',
        column2='user_id',
        compute='_compute_procurement_user_ids', store=True,
        help="Users assigned as procurement users in the enquiry lines")
    used_part_code_ids = fields.Many2many('product.part.code', compute='_compute_used_part_code_ids', store=True)
    sale_order_id = fields.Many2one("sale.order", string="Sales Order ID")

    @api.depends('enquiry_line_ids.procurement_user_id', 'enquiry_line_ids.state', 'enquiry_line_ids')
    def _compute_pending_user_ids(self):
        for rec in self:
            if rec.id:
                pending_lines = self.env['sale.enquiry.line'].sudo().search([('enquiry_id', '=', rec.id), ('state', '!=', 'quoted_to_salesperson'), ('is_foc_line', '=', False)])
                rec.pending_user_ids = [(6, 0, pending_lines.mapped('procurement_user_id.id'))]

    @api.depends('enquiry_line_ids.part_code_id', 'enquiry_line_ids')
    def _compute_used_part_code_ids(self):
        for rec in self:
            rec.used_part_code_ids = rec.enquiry_line_ids.mapped('part_code_id')

    @api.depends('enquiry_line_ids.procurement_user_id')
    def _compute_procurement_user_ids(self):
        for enquiry in self:
            # Get existing procurement users to preserve them
            existing_users = enquiry.procurement_user_ids
            # Get current procurement users from lines
            current_users = enquiry.enquiry_line_ids.mapped('procurement_user_id')

            # Combine both sets of users without duplicates
            all_users = existing_users | current_users

            # Only update if there are new users to add
            if current_users - existing_users:
                enquiry.procurement_user_ids = [(6, 0, all_users.ids)]

    @api.depends('kit_source_done_user_ids')
    def _compute_show_kit_source_button(self):
        current_user = self.env.user
        for record in self:
            record.show_kit_source_button = current_user not in record.kit_source_done_user_ids

    @api.depends('partner_id')
    def _compute_payment_term_id(self):
        for enquiry in self:
            enquiry = enquiry.with_company(enquiry.company_id)
            enquiry.payment_term_id = enquiry.partner_id.property_payment_term_id

    @api.model
    def web_search_read(self, domain, specification, offset=0, limit=None, order=None, count_limit=None):
        if self.env.context.get('is_pending_quote'):
            domain += [('pending_user_ids', 'in', self._uid)]
        return super(SalesEnquiry, self).web_search_read(domain, specification, offset, limit, order,count_limit)

    def reset_sequence_if_first_of_day(self):
        """Check if it's the first inquiry of the user's local day and reset the sequence if needed"""
        sequence = self.env['ir.sequence'].search([('code', '=', 'sale.enquiry')], limit=1)
        if sequence:
            # Get user timezone
            user_tz = self.env.user.tz or 'UTC'
            tz = timezone(user_tz)

            # Get current date in user's timezone
            today = datetime.now(tz).date()

            # Store last reset date separately for each user
            reset_key = f"last_reset_date_{self.env.user.id}"
            last_reset_date = self.env['ir.config_parameter'].sudo().get_param(reset_key)

            # If no reset date is found, reset the sequence
            if not last_reset_date or last_reset_date != today.isoformat():
                sequence.sudo().write({'number_next': 1})
                self.env['ir.config_parameter'].sudo().set_param(reset_key, today.isoformat())

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if 'name' not in vals or vals['name'] == _('New'):
                self.reset_sequence_if_first_of_day()
                user_code = self.env.user.code or ''
                # Get current date
                today = datetime.now(timezone(self.env.user.tz or 'UTC'))
                year = today.strftime('%y')  # Last 2 digits of the year
                month = today.strftime('%m')  # Month
                day = today.strftime('%d')  # Day

                # Generate the next sequence number
                sequence = self.env['ir.sequence'].next_by_code('sale.enquiry')

                # Build the final sequence
                custom_seq = f"{user_code}RFQ{year}{month}{day}{sequence}"

                vals['name'] = custom_seq
                vals['show_update_currency_btn'] = True
        enquiry = super().create(vals_list)
        if enquiry.partner_id.customer_stage == 'open_lead':
            enquiry.partner_id.customer_stage = 'sent_rfq'
        return enquiry

    def action_update_prices(self):
        """Update currency rate based on the selected date"""
        self.ensure_one()
        for line in self.enquiry_line_ids:
            if line.quoted_price:
                price = self.previous_currency_id._convert(
                    from_amount=line.quoted_price,
                    to_currency=self.currency_id,
                    company=self.company_id,
                    date=fields.Date.today()
                )
                line.quoted_price = price
        self.show_update_currency_btn = True
        self.previous_currency_id = self.currency_id.id
        return True

    @api.onchange('partner_id')
    def _onchange_partner_id(self):
        if self.partner_id:
            self.currency_id = self.partner_id.currency_id.id if self.partner_id.currency_id else False

    # @api.onchange('currency_id')
    # def _onchange_currency(self):
    #     if self.currency_id:
    #         self.show_update_currency_btn = False

    @api.depends('partner_id')
    def _compute_partner_shipping_id(self):
        for order in self:
            order.partner_shipping_id = order.partner_id.address_get(['delivery'])[
                'delivery'] if order.partner_id else False

    @api.depends('partner_id')
    def _compute_contact_person(self):
        for order in self:
            order.contact_person_id = (
                next(
                    (child for child in order.partner_id.child_ids if child.type == 'contact'),
                    False
                ) if order.partner_id else False
            )

    def action_submit(self):
        if not self.enquiry_line_ids:
            raise UserError(_("You cannot submit the enquiry without any lines."))
        for line in self.enquiry_line_ids:
            line.state = 'pending'
        self.state = 'pending'
        # self.is_pending_quote = True

        if self.env.user.has_group('ni_sales_enquiry.group_walkin_stores'):
            lines = self.enquiry_line_ids.filtered(
                lambda l: l.product_id.type == 'consu' and l.product_id.is_storable and l.in_stock_qty == 0)
            if lines:
                raise ValidationError(_("You cannot submit the enquiry with products that are not in stock."))
        else:
            action = self.env.ref('ni_sales_enquiry.sale_enquiry_act_window')
            message = f"Please quote for enquiry(<a href='/odoo/action-{action.id}/{self.id}'>{self.name}</a>)."
            for user in self.procurement_user_ids:
                channel = self.env['discuss.channel'].channel_get(partners_to=[user.partner_id.id])
                channel_id = self.env['discuss.channel'].browse(channel["id"])

                channel_id.message_post(
                    body=Markup(message),
                    message_type='comment',
                    subtype_xmlid='mail.mt_comment'
                )

    def action_enquiry_lost(self):
        enquiry = self.env['sale.enquiry'].browse(self.env.context.get('active_ids', []))
        return {
            'type': 'ir.actions.act_window',
            'name': _('Lost Reason'),
            'res_model': 'sales.enquiry.lost.reason',
            'view_mode': 'form',
            'target': 'new',
            'context': {'active_id': enquiry.id},
        }

    # def action_won(self):
    #     self.state = 'won'

    def action_create_purchase_quote(self):
        # Get the brands assigned to the current user
        user_brands = self.env.user.brand_ids
        # If user has specific brands assigned, filter the enquiry lines accordingly
        if user_brands:
            filtered_lines = self.enquiry_line_ids.filtered(lambda line: line.product_id.brand_id in user_brands)
        else:
            filtered_lines = self.enquiry_line_ids  # If no brand restriction, show all lines

        # Prepare wizard values
        wizard_vals = {
            'enquiry_id': self.id,
            'rfq_create_wizard_line_ids': [(0, 0, {
                'sequence_number': line.sequence_number,
                'product_id': line.product_id.id,
                'product_condition_id': line.product_condition_id.id,
                'part_code_id': line.part_code_id.id,
                'product_qty': line.product_uom_qty,
                'is_subproduct': line.is_subproduct,
                'enquiry_line_id': line.id
            }) for line in filtered_lines],
        }

        rfq_create_wizard = self.env['rfq.create.wizard'].create(wizard_vals)
        return {
            'name': _('Create Purchase Quote'),
            'view_mode': 'form',
            'res_model': 'rfq.create.wizard',
            'res_id': rfq_create_wizard.id,
            'type': 'ir.actions.act_window',
            'target': 'new',
        }

    def action_view_purchase_order(self):
        action = {
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.order',
            'target': 'current',
        }
        if len(self.purchase_order_ids) == 1:
            action.update({
                'view_mode': 'form',
                'res_id': self.purchase_order_ids.id,
            })
        else:
            action.update({
                'view_mode': 'list,form',
                'domain': [('id', 'in', self.purchase_order_ids.ids)],
            })

        return action

    @api.depends('partner_id', 'user_id')
    def _compute_team_leader_id(self):
        cached_teams = {}
        for order in self:
            default_team_id = self.env.context.get('default_team_id', False) or order.team_leader_id.id
            user_id = order.user_id.id
            company_id = order.company_id.id
            key = (default_team_id, user_id, company_id)
            if key not in cached_teams:
                cached_teams[key] = self.env['crm.team'].with_context(
                    default_team_id=default_team_id,
                )._get_default_team_id(
                    user_id=user_id,
                    domain=self.env['crm.team']._check_company_domain(company_id),
                )
                team_id = cached_teams[key]
                order.team_leader_id = team_id.user_id.id

    def _get_lang(self):
        self.ensure_one()
        if self.partner_id.lang and not self.partner_id.is_public:
            return self.partner_id.lang

        return self.env.lang

    def action_view_line(self):
        return {
            'name': 'Sale Enquiry Lines',
            'type': 'ir.actions.act_window',
            'res_model': 'sale.enquiry.line',
            'view_mode': 'list',
            'target': 'new',
            'domain': [('enquiry_id', '=', self.id)],
        }

    def action_create_kit_source(self):
        purchase = self.purchase_order_ids.filtered(
            lambda po: po.user_id == self.env.user)
        if self.kit_source_ids.filtered(lambda l: l.create_uid == self.env.user):
            cancel_purchase = purchase.filtered(lambda po: po.state == 'cancel')
            if cancel_purchase:
                cancel_purchase.write({'state': 'quote_received'})
            lines = purchase.mapped('order_line')
            for line in lines:
                if line.product_qty != line.actual_po_qty:
                    line.product_qty = line.actual_po_qty
        purchase = purchase.filtered(lambda po: po.state == 'quote_received')
        if len(purchase) <= 1:
            raise UserError(
                "To create a kit source, you need at least two purchase quotes with 'quote received' status. Please create additional purchase quote before proceeding.")

        sale_order_id = self.sudo().sale_order_id

        ks_vals = {
            'enquiry_id': self.id,
            'so_id': sale_order_id and sale_order_id.id or False,
            'so_ref_date': sale_order_id and sale_order_id.date_order.date() or False,
            'sales_person_id': self.user_id.id,
            'procurement_user_id': self.env.user.id,
            'kit_ref_date': fields.Date.today()
        }

        purchase_line_ids = purchase.mapped('order_line')

        kit_source_line_vals = []
        so_lines = sale_order_id.order_line if sale_order_id else self.env['sale.order.line']
        for line in purchase_line_ids:
            # Find matching sale order line (by product_id, adjust as needed)
            so_line = so_lines.filtered(lambda sol: sol.product_id.id == line.product_id.id)
            so_line = so_line[0] if so_line else self.env['sale.order.line']
            kit_source_line_vals.append((0, 0, {
                'purchase_order_id': line.order_id.id,
                'purchase_order_no': line.order_id.name,
                'purchase_line_id': line.id,
                'partner_id': line.partner_id.id,
                'product_id': line.product_id.id,
                'part_code_id': line.part_code_id.id,
                'product_qty': line.product_qty,
                'brand_id': line.brand_id.id,
                'po_unit_price': line.price_unit,
                'po_price_tax': line.price_tax,
                'so_unit_price': so_line.price_unit,
                'so_price_tax': so_line.price_tax,
                'is_subproduct': line.is_subproduct,
                'incoterms': line.incoterms,
                'lead_time_id': line.lead_time_id.id,
                'payment_term_id': line.payment_term_id.id,
                'currency_id': line.currency_id.id or False,
                'po_total_value': line.price_total,
                'so_total_value': so_line.price_total,
                'so_line_id': so_line.id,
            }))
        ks_vals['kit_source_line_ids'] = kit_source_line_vals

        # Create kit source record
        ks = self.env['kit.source'].create(ks_vals)
        if ks:
            self.kit_source_ids += ks
            self.kit_source_done_user_ids = [(4, self.env.user.id)]
            return {
                'name': 'Kit Source',
                'type': 'ir.actions.act_window',
                'res_model': 'kit.source',
                'res_id': ks.id,
                'view_mode': 'form',
                'target': 'current',
            }
        return None

    def action_view_kit_source(self):
        self.ensure_one()
        action = {
            'name': 'Kit Source',
            'type': 'ir.actions.act_window',
            'res_model': 'kit.source',
            'view_mode': 'list,form',
            'target': 'current',
        }
        if len(self.kit_source_ids) == 1:
            action.update({
                'view_mode': 'form',
                'res_id': self.kit_source_ids.id,
            })
        else:
            action.update({
                'domain': [('id', 'in', self.kit_source_ids.ids)],
            })
        return action

    def action_executed(self):
        self.state = 'executed'

    @api.model
    def _get_default_rfq_type(self):
        group_walkin = self.env.ref('ni_sales_enquiry.group_walkin_stores', raise_if_not_found=False)
        if group_walkin and group_walkin in self.env.user.groups_id:
            walkin_type = self.env['rfq.type'].search([('name', '=', 'WALK-IN CUSTOMER')], limit=1)
            return walkin_type.id if walkin_type else False
        return False

    def action_view_sale_order(self):
        self.ensure_one()
        action = {
            'name': 'Sales Order',
            'type': 'ir.actions.act_window',
            'res_model': 'sale.order',
            'view_mode': 'form',
            'res_id': self.sale_order_id.id,
            'target': 'current',
        }
        return action