<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="sale_enquiry_form_inherit" model="ir.ui.view">
            <field name="name">sale.enquiry.inherit</field>
            <field name="model">sale.enquiry</field>
            <field name="inherit_id" ref="ni_sales_enquiry.sale_enquiry_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@id='btn_action_create_purchase_quotation']" position="after">
                    <button name="create_quotation_confirmation_wizard" id="btn_create_quotation" type="object"
                            string="Create Quotation"
                            class="btn-primary" invisible="state not in ['quoted_to_salesperson','quoted','pi_shared'] or not show_generate_proforma_invoice_button"
                            groups="sales_team.group_sale_salesman,!ni_sales_enquiry.group_walkin_stores"/>
                    <button name="create_quotation_store" id="btn_create_quotation_store" type="object"
                            string="Create Quotation"
                            class="btn-primary" invisible="state not in ['pending']"
                            groups="ni_sales_enquiry.group_walkin_stores"/>
                    <button name="generate_proforma_invoice" id="btn_generate_proforma_invoice" type="object"
                            string="Generate Proforma Invoice"
                            class="btn-primary" invisible="not show_generate_proforma_invoice_button or state not in ['quoted', 'pi_shared']"
                            groups="sales_team.group_sale_salesman"/>
                    <button name="action_edit_quoted_price" id="edit_quoted_price_btn" type="object"
                            string="Update Quoting Price"
                            class="btn-primary"
                            invisible="current_user_editing or is_edited_user or state not in ['pending', 'quoted']"
                            groups="purchase.group_purchase_user,purchase.group_purchase_manager"/>
                    <button name="action_quoted_to_salesperson" id="quoted_to_salesperson_btn" type="object"
                            string="Send Quote To Salesperson"
                            class="btn-primary"
                            confirm="Are you sure you want to send quote to salesperson?"
                            invisible="is_quoted_to_salesperson or is_line_quoted_to_salesperson or current_user_editing"
                            groups="purchase.group_purchase_user,purchase.group_purchase_manager"/>
                    <button name="show_price_history" id="show_history_salesperson_btn" type="object"
                            string="Show Quote Price History"
                            class="btn-primary"
                            invisible="not show_price_history_button"
                            groups="purchase.group_purchase_user,purchase.group_purchase_manager"/>
                    <button name="show_mmr" id="show_mmr_btn" type="object"
                            string="Show MMR"
                            class="btn-primary"
                            invisible="state in ['new', 'pending', 'outstanding', 'lost'] or (state == 'quoted_to_salesperson' and not quotation_ids)"
                            groups="ni_sales_enquiry.group_sale_enquiry_manager"/>
                </xpath>
                <xpath expr="//button[@name='action_view_kit_source']" position="after">
                    <button class="oe_stat_button" type="object"
                            invisible="not quotation_ids"
                            name="action_view_quotation" icon="fa-pencil-square-o">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Quotations</span>
                        </div>
                    </button>
                    <button class="oe_stat_button" type="object"
                            invisible="not pi_ids"
                            name="action_view_proforma_invoice" icon="fa-pencil-square-o">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Proforma Invoice</span>
                        </div>
                    </button>
                    <button class="oe_stat_button" type="object"
                            invisible="not sale_order_id"
                            name="action_view_sale_order" icon="fa-pencil-square-o">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Sales Order</span>
                        </div>
                    </button>
                </xpath>
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='product_uom_qty']" position="after">
                    <field name="quoted_price" readonly="is_foc_line or not parent.current_user_editing"
                           widget="monetary" column_invisible="parent.state == 'new'"/>
                    <field name="is_quoted" readonly="1" column_invisible="1"/>
                    <field name="is_editable" readonly="1" column_invisible="1"/>
                </xpath>
<!--                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='sequence_number']" position="attributes">-->
<!--                    <attribute name="readonly">not is_editable</attribute>-->
<!--                </xpath>-->
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='part_code_id']" position="attributes">
                    <attribute name="readonly">not is_editable</attribute>
                </xpath>
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='product_id']" position="attributes">
                    <attribute name="readonly">not is_editable</attribute>
                </xpath>
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='product_condition_id']" position="attributes">
                    <attribute name="readonly">not is_editable</attribute>
                </xpath>
<!--                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='product_category_id']" position="attributes">-->
<!--                    <attribute name="readonly">not is_editable</attribute>-->
<!--                </xpath>-->
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='product_uom_qty']" position="attributes">
                    <attribute name="readonly">not is_editable</attribute>
                </xpath>
<!--                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='target_price']" position="attributes">-->
<!--                    <attribute name="readonly">not is_editable</attribute>-->
<!--                </xpath>-->
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='product_uom']" position="attributes">
                    <attribute name="readonly">not is_editable</attribute>
                </xpath>
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='currency_id']" position="attributes">
                    <attribute name="readonly">not is_editable</attribute>
                </xpath>
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='company_id']" position="attributes">
                    <attribute name="readonly">not is_editable</attribute>
                </xpath>
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='product_uom_qty']" position="after">
                    <field name="received_in_warehouse_qty" readonly="1"
                           column_invisible="parent.state not in ['inbound','grn','outbound','delivery_note', 'executed']"/>
                    <field name="estimated_arrival_date" readonly="1"
                           column_invisible="parent.state not in ['inbound','grn','outbound','delivery_note', 'executed']"/>
                </xpath>
                <xpath expr="//field[@name='enquiry_line_ids']/list/field[@name='target_price']" position="after">
                    <field name="lead_time_id"
                           readonly="parent.state not in ['pending','quoted_to_salesperson','quoted'] or not parent.current_user_editing"
                           column_invisible="parent.state not in ['pending','quoted_to_salesperson','quoted']"
                           required="parent.state in ['pending','quoted_to_salesperson','quoted']"/>
                </xpath>
                <xpath expr="//notebook" position="inside">
                    <page string="Quoted Price History" invisible="1"
                          groups="purchase.group_purchase_user,purchase.group_purchase_manager">
                        <field name="quoted_price_history_ids" readonly="1">
                            <list edit="0" delete="0" create="0">
                                <field name="version"/>
                                <field name="create_date" string="Updated On"/>
                                <field name="create_uid" optional="hide"/>
                                <field name="part_code_id"/>
                                <field name="old_quoted_price"/>
                                <field name="new_quoted_price"/>
                            </list>
                        </field>
                    </page>
                </xpath>
            </field>
        </record>

        <record id="sale_enquiry_list_view_to_procure" model="ir.ui.view">
            <field name="name">sale.enquiry.list</field>
            <field name="model">sale.enquiry</field>
            <field name="arch" type="xml">
                <list string="Sales Enquiry" delete="0" duplicate="0">
                    <field name="name"/>
                    <field name="sale_order_id"/>
                    <field name="partner_id"/>
                    <field name="user_id" widget="many2one_avatar_user"/>
                    <field name="team_leader_id"/>
                    <field name="enquiry_date"/>
                    <field name="state"
                           decoration-success="state == 'delivered'"
                           decoration-info="state == 'so_generated'"
                           decoration-primary="state == 'pending'"
                           decoration-danger="state == 'lost'"
                           decoration-muted="state == 'sourcing'"
                           widget="badge"/>
                    <button name="action_view_line" type="object" string="View Lines" class="oe_highlight"/>
                </list>
            </field>
        </record>
        <!-- New action for SO Generated Enquiries -->
        <record id="action_sale_enquiry_so_generated" model="ir.actions.act_window">
            <field name="name">Orders to Procure</field>
            <field name="res_model">sale.enquiry</field>
            <field name="view_mode">list,form</field>
            <field name="view_id" ref="sale_enquiry_list_view_to_procure"/>
            <field name="domain">[('state', 'in', ['so_generated','sourcing','procurement'])]</field>
        </record>

        <!-- New menu item for purchase people -->
        <menuitem id="menu_sale_enquiry_so_generated"
                  name="Orders to Procure"
                  action="action_sale_enquiry_so_generated"
                  parent="ni_sales_enquiry.sale_enquiry_root"
                  sequence="4"
                  groups="purchase.group_purchase_user,purchase.group_purchase_manager"/>
    </data>
</odoo>