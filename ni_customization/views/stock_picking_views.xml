<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_picking_form_view_inherit" model="ir.ui.view">
        <field name="name">stock.picking.form.view.inherit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="delete">0</attribute>
                <attribute name="duplicate">0</attribute>
            </xpath>
            <xpath expr="//field[@name='move_ids_without_package']/list/field[@name='product_id']" position="before">
                <field name="part_code_id" optional="show" options="{'no_open': True}"
                       readonly="parent.purchase_id or parent.sale_id" required="1"/>
            </xpath>
            <xpath expr="//field[@name='move_ids_without_package']/list/field[@name='product_id']" position="after">
                <!--                <field name="product_condition" optional="show"/>-->
                <field name="name" readonly="1"/>
                <field name="brand_id" optional="show" options="{'no_open': True}"/>
            </xpath>
            <xpath expr="//field[@name='move_ids_without_package']/list/field[@name='product_id']"
                   position="attributes">
                <attribute name="string">Description</attribute>
                <attribute name="readonly">1</attribute>
                <attribute name="column_invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="readonly">state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="readonly">state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='location_id']" position="attributes">
                <attribute name="readonly">state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='location_dest_id']" position="attributes">
                <attribute name="readonly">state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='location_id'][2]" position="attributes">
                <attribute name="readonly">state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='location_dest_id'][2]" position="attributes">
                <attribute name="readonly">state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='scheduled_date']" position="attributes">
                <attribute name="readonly">state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='move_ids_without_package']/list/field[@name='part_code_id']" position="attributes">
                <attribute name="readonly">parent.state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='move_ids_without_package']/list/field[@name='quantity']" position="attributes">
                <attribute name="readonly">parent.state not in ['draft']</attribute>
            </xpath>
            <xpath expr="//field[@name='carrier_id']" position="before">
                <field name="mode_of_transport" required="purchase_enquiry_id or sale_enquiry_id"
                       readonly="state == 'done'"/>
            </xpath>
            <xpath expr="//field[@name='carrier_id']" position="attributes">
                <attribute name="required">purchase_enquiry_id or sale_enquiry_id</attribute>
            </xpath>
            <xpath expr="//field[@name='carrier_tracking_ref']" position="attributes">
                <attribute name="required">purchase_enquiry_id or sale_enquiry_id</attribute>
            </xpath>
            <xpath expr="//field[@name='group_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='sale_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='carrier_tracking_ref']" position="attributes">
                <attribute name="readonly">state == 'done'</attribute>
            </xpath>
            <xpath expr="//field[@name='origin']" position="replace">
                <field name="sale_id" invisible="picking_type_code != 'outgoing'" string="Sale Order" readonly="1"/>
                <field name="show_serial_number" invisible="picking_type_code != 'outgoing'"/>
                <field name="purchase_id" invisible="picking_type_code != 'incoming'" string="Purchase Order"
                       readonly="1"/>
                <field name="so_id" invisible="picking_type_code != 'incoming'" string="Sale Order" options="{'no_open': True}"
                       readonly="1"/>
                <field name="rma_reason_id" invisible="not rma_reason_id" readonly="1" options="{'no_open': True}"/>
            </xpath>
            <xpath expr="//field[@name='state']" position="replace">
                <field name="receipt_state" widget="statusbar" invisible="picking_type_code != 'incoming'"
                       statusbar_visible="draft,inbound,grn"/>
            </xpath>
            <xpath expr="//field[@name='state']" position="replace">
                <field name="state" widget="statusbar" invisible="picking_type_code in ['incoming','outgoing']"
                       statusbar_visible="draft,assigned,done"/>
                <field name="delivery_state" widget="statusbar" invisible="picking_type_code != 'outgoing'"
                       statusbar_visible="draft,outbound,delivery_note"/>
            </xpath>
            <xpath expr="//page[@name='extra']" position="attributes">
                <attribute name="string">Shipment Details</attribute>
            </xpath>
            <xpath expr="//field[@name='move_type']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//group[@name='other_infos']" position="attributes">
                <attribute name="groups">base.group_no_one</attribute>
            </xpath>
            <xpath expr="//label[@for='shipping_weight']" position="attributes">
                <attribute name="groups">base.group_no_one</attribute>
            </xpath>
            <xpath expr="//field[@name='shipping_weight']/parent::div" position="attributes">
                <attribute name="groups">base.group_no_one</attribute>
            </xpath>
            <xpath expr="//label[@for='weight']" position="attributes">
                <attribute name="groups">base.group_no_one</attribute>
            </xpath>
            <xpath expr="//field[@name='weight']/parent::div" position="attributes">
                <attribute name="groups">base.group_no_one</attribute>
            </xpath>
            <xpath expr="//button[@name='%(stock.act_stock_return_picking)d']" position="attributes">
                <attribute name="string">RMA Return</attribute>
            </xpath>
            <xpath expr="//button[@name='%(stock.act_stock_return_picking)d']" position="after">
                <button name="button_mark_as_delivered" string="Mark as Delivered" type="object"
                        invisible="delivery_state != 'delivery_note'"/>
            </xpath>
            <xpath expr="//button[@name='button_validate']" position="attributes">
                <attribute name="confirm">Are you sure you want to validate this?</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_stock_move_line_detailed_operation_tree_inherit_ni" model="ir.ui.view">
        <field name="name">stock.move.line.operations.list.inherit.ni</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="part_code_id" optional="show" options="{'no_open': True}"/>
                <field name="product_condition_id" optional="show"/>
                <field name="brand_id" optional="show" options="{'no_open': True}"/>
            </xpath>
        </field>
    </record>

    <record id="stock_picktree_ni" model="ir.ui.view">
        <field name="name">stock.picking.list.inherit.ni</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.vpicktree"/>
        <field name="arch" type="xml">
            <xpath expr="//list" position="attributes">
                <attribute name="delete">0</attribute>
                <attribute name="duplicate">0</attribute>
            </xpath>
            <field name="company_id" position="attributes">
                <attribute name="optional">hide</attribute>
            </field>
            <field name="origin" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </field>
        </field>
    </record>

    <record id="receipt_view_list_ni" model="ir.ui.view">
        <field name="name">stock.picking.list.ni</field>
        <field name="model">stock.picking</field>
        <field name="arch" type="xml">
            <list string="Picking list" sample="1" js_class="stock_list_view" delete="0" duplicate="0">
                <header>
                    <button name="do_unreserve" type="object" string="Unreserve"/>
                    <button name="action_assign" type="object" string="Check Availability"/>
                </header>
                <field name="company_id" column_invisible="True"/>
                <field name="priority" optional="show" widget="priority" nolabel="1"/>
                <field name="name" decoration-bf="1"/>
                <field name="location_id" options="{'no_create': True}" string="From"
                       groups="stock.group_stock_multi_locations" optional="hide" readonly="receipt_state == 'grn'"/>
                <field name="location_dest_id" options="{'no_create': True}" string="To"
                       groups="stock.group_stock_multi_locations" optional="hide" readonly="receipt_state == 'grn'"/>
                <field name="partner_id" optional="show" string="Supplier Company Name" readonly="receipt_state in ['cancel', 'grn']"/>
                <field name="is_signed" string="Signed" optional="hide" groups="stock.group_stock_sign_delivery"/>
                <field name="user_id" optional="hide" widget="many2one_avatar_user"
                       readonly="receipt_state in ['cancel', 'grn']"/>
                <field name="scheduled_date" optional="show" widget="remaining_days"
                       invisible="receipt_state in ('grn', 'cancel')" readonly="receipt_state in ['cancel', 'grn']"/>
                <field name="picking_type_code" column_invisible="True"/>
                <field name="products_availability_state" column_invisible="True" options='{"lazy": true}'/>
                <field name="date_deadline" optional="hide" widget="remaining_days"
                       invisible="receipt_state in ('grn', 'cancel')"/>
                <field name="date_done" string="Effective Date" optional="hide"/>
                <field name="purchase_id" optional="show" readonly="1" string="Purchase Order"/>
                <field name="carrier_id" optional="show" readonly="1" string="Carrier"/>
                <field name="carrier_tracking_ref" optional="show" readonly="1" string="Tracking Reference"/>
                <field name="backorder_id" optional="hide"/>
                <field name="picking_type_id" optional="hide"/>
                <field name="receipt_state" optional="show" widget="badge"
                       decoration-danger="receipt_state=='cancel'"
                       decoration-info="receipt_state== 'inbound'"
                       decoration-muted="receipt_state == 'draft'"
                       decoration-success="receipt_state == 'grn'" string="Status"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
                <field name="json_popover" widget="stock_rescheduling_popover" nolabel="1"
                       invisible="not json_popover"/>
            </list>
        </field>
    </record>

    <record id="delivery_view_list_ni" model="ir.ui.view">
        <field name="name">stock.picking.list.delivery.ni</field>
        <field name="model">stock.picking</field>
        <field name="arch" type="xml">
            <list string="Picking list" sample="1" js_class="stock_list_view" delete="0" duplicate="0">
                <header>
                    <button name="do_unreserve" type="object" string="Unreserve"/>
                    <button name="action_assign" type="object" string="Check Availability"/>
                </header>
                <field name="company_id" column_invisible="True"/>
                <field name="priority" optional="show" widget="priority" nolabel="1"/>
                <field name="name" decoration-bf="1"/>
                <field name="location_id" options="{'no_create': True}" string="From"
                       groups="stock.group_stock_multi_locations" optional="hide"
                       readonly="delivery_state == 'delivery_note'"/>
                <field name="location_dest_id" options="{'no_create': True}" string="To"
                       groups="stock.group_stock_multi_locations" optional="hide"
                       readonly="delivery_state == 'delivery_note'"/>
                <field name="partner_id" optional="show" string="Customer Company Name" readonly="delivery_state in ['cancel', 'delivery_note']"/>
                <field name="is_signed" string="Signed" optional="hide" groups="stock.group_stock_sign_delivery"/>
                <field name="user_id" optional="hide" widget="many2one_avatar_user"
                       readonly="delivery_state in ['cancel', 'grn']"/>
                <field name="scheduled_date" optional="show" widget="remaining_days"
                       invisible="delivery_state in ('delivery_note', 'cancel')"
                       readonly="delivery_state in ['cancel', 'delivery_note']"/>
                <field name="picking_type_code" column_invisible="True"/>
                <field name="products_availability_state" column_invisible="True" options='{"lazy": true}'/>
                <field name="date_deadline" optional="hide" widget="remaining_days"
                       invisible="delivery_state in ('delivery_note', 'cancel')"/>
                <field name="date_done" string="Effective Date" optional="hide"/>
                <field name="sale_id" optional="show" readonly="1" string="Sale Order"/>
                <field name="carrier_id" optional="show" readonly="1" string="Carrier"/>
                <field name="carrier_tracking_ref" optional="show" readonly="1" string="Tracking Reference"/>
                <field name="backorder_id" optional="hide"/>
                <field name="picking_type_id" optional="hide"/>
                <field name="delivery_state" optional="show" widget="badge"
                       decoration-danger="delivery_state=='cancel'"
                       decoration-info="delivery_state== 'outbound'"
                       decoration-muted="delivery_state == 'deliver_note'"
                       decoration-success="delivery_state == 'delivered'" string="Status"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
                <field name="json_popover" widget="stock_rescheduling_popover" nolabel="1"
                       invisible="not json_popover"/>
            </list>
        </field>
    </record>

    <record id="stock.action_picking_tree_incoming" model="ir.actions.act_window">
        <field name="domain">[('picking_type_code', '=', 'incoming')]</field>
        <field name="view_id" ref="receipt_view_list_ni"/>
    </record>

    <record id="stock.action_picking_tree_outgoing" model="ir.actions.act_window">
        <field name="domain">[('picking_type_code', '=', 'outgoing')]</field>
        <field name="view_id" ref="delivery_view_list_ni"/>
    </record>

    <record id="view_backorder_confirmation_ni" model="ir.ui.view">
        <field name="name">stock_barcode_backorder_confirmation_ni</field>
        <field name="model">stock.backorder.confirmation</field>
        <field name="inherit_id" ref="stock.view_backorder_confirmation"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="string">Pending Deliveries Creation</attribute>
            </xpath>
            <xpath expr="//button[@name='process']" position="attributes">
                <attribute name="string">Create Pending Deliveries</attribute>
            </xpath>
            <xpath expr="//button[@name='process_cancel_backorder']" position="attributes">
                <attribute name="string">No Pending Deliveries</attribute>
            </xpath>
            <xpath expr="//div[hasclass('text-muted')]" position="replace">
                <div colspan="2" class="text-muted">
                    Create a Pending Delivery if you expect to process the remaining
                    products later. Do not create a pending delivery if you will not
                    process the remaining products.
                </div>
            </xpath>
        </field>
    </record>

    <record id="method_action_picking_tree_incoming" model="ir.actions.server">
        <field name="name">stock.method_action_picking_tree_incoming</field>
        <field name="model_id" ref="stock.model_stock_picking"/>
        <field name="state">code</field>
        <field name="code">action = model.get_action_picking_tree_incoming(pending_receipt=True)</field>
    </record>

    <record id="method_action_picking_tree_outgoing" model="ir.actions.server">
        <field name="name">stock.method_action_picking_tree_outgoing</field>
        <field name="model_id" ref="stock.model_stock_picking"/>
        <field name="state">code</field>
        <field name="code">action = model.get_action_picking_tree_outgoing(pending_delivery=True)</field>
    </record>

    <menuitem id="direct_in_picking" name="Receipts"
              action="method_action_picking_tree_incoming" sequence="20"
              groups="stock.group_stock_manager,stock.group_stock_user"/>
    <menuitem id="direct_out_picking" name="Deliveries"
              action="method_action_picking_tree_outgoing" sequence="21"
              groups="stock.group_stock_manager,stock.group_stock_user"/>

</odoo>