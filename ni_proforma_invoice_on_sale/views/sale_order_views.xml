<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="sale_order_form_view_customisation_pi" model="ir.ui.view">
            <field name="name">sale.order.customisation.form.pi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@id='send_proforma_primary']" position="before">
<!--                    <button name="action_generate_pi" id="generate_pi" type="object"-->
<!--                            confirm="Are you sure you want to generate PRO-FORMA Invoice?"-->
<!--                            string="Generate PRO-FORMA Invoice" class="btn-primary"-->
<!--                            invisible="state != 'sent' or pi_ref_no"/>-->
                </xpath>
                <xpath expr="//button[@name='action_confirm'][1]" position="after">
                    <field name="so_from_pi_created" invisible="1"/>
                    <button name="action_send_quotation_for_approve" string="Send For Approval" type="object"

                            class="oe_highlight" invisible="state in ['to_approve','sale','cancel','draft'] or pi_state!='pi_sent' or not so_from_pi_created"/>
                    <button name="action_confirm_sale_quote" invisible="state != 'to_approve'" string="Approve"
                            type="object"
                            confirm="Are you sure you want to approve sale order?"
                            groups="ni_customization.group_so_approval"
                            context="{'validate_analytic': True}"
                            class="oe_highlight"/>
                </xpath>
                <xpath expr="//button[@name='action_confirm'][1]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//button[@name='action_confirm'][2]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//button[@id='send_proforma_primary']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//button[@id='send_proforma']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//button[@name='action_confirm'][1]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//button[@name='action_confirm'][2]" position="replace">
<!--                    <button name="action_confirm" id="action_confirm" data-hotkey="q"-->
<!--                            confirm="Are you sure you want to create sale order?"-->
<!--                            string="Confirm" class="btn-primary" type="object" context="{'validate_analytic': True}"-->
<!--                            invisible="pi_state != 'pi_sent' or state == 'sale'"/>-->
                    <button name="action_confirm" id="action_confirm" data-hotkey="q"
                            confirm="Are you sure you want to create sale order?"
                            string="Confirm" class="btn-primary" type="object" context="{'validate_analytic': True}"
                            invisible="state not in  ['draft','sent']"
                            groups="ni_sales_enquiry.group_walkin_stores"/>
                </xpath>
<!--                <xpath expr="//div[hasclass('oe_button_box')]" position="inside">-->
<!--                    <button name="action_view_proforma_invoice"-->
<!--                            type="object"-->
<!--                            class="oe_stat_button"-->
<!--                            icon="fa-pencil-square-o"-->
<!--                            invisible="not pi_ref_no">-->
<!--                        Proforma Invoice-->
<!--                    </button>-->
<!--                </xpath>-->
                <xpath expr="//field[@name='state']" position="attributes">
                    <attribute name="statusbar_visible">draft,sent,pi,pi_shared_client,draft_order,to_approve,sale</attribute>
                </xpath>
            </field>
        </record>


        <record id="view_order_form_ni" model="ir.ui.view">
            <field name="name">sale.order.form</field>
            <field name="model">sale.order</field>
            <field name="arch" type="xml">
                <form string="Proforma Invoice" class="o_sale_order" delete="0" duplicate="0" create="0">
                    <header>
                        <field name="locked" invisible="1"/>
                        <field name="authorized_transaction_ids" invisible="1"/>
                        <button name="action_submit_pi" id="submit_pi" type="object"
                                string="Submit" class="btn-primary"
                                confirm="Are you sure you want to submit this proforma invoice?"
                                invisible="pi_state != 'draft'"/>
                        <button name="action_cancel" id="action_cancel" type="object"
                                string="Cancel" class="btn-primary"
                                confirm="Are you sure you want to cancel this proforma invoice?"
                                invisible="pi_state != 'draft'"/>
                        <button name="action_pi_approve" id="pi_approve" type="object"
                                string="Approve" class="btn-primary"
                                groups="sale.group_proforma_sales"
                                confirm="Are you sure you want to approve this proforma invoice?"
                                invisible="pi_state != 'in_review'"/>
                        <button name="action_reset_pi" id="action_reset_pi" type="object"
                                string="Reset To Approve" class="btn-primary"
                                groups="sale.group_proforma_sales"
                                confirm="Are you sure you want to reset this proforma invoice?"
                                invisible="pi_state != 'rejected'"/>
                        <button name="action_pi_reject" id="pi_reject" type="object"
                                string="Reject" class="btn-primary"
                                groups="sale.group_proforma_sales"
                                invisible="pi_state != 'in_review'"/>
                        <button name="action_quotation_send" id="send_proforma_primary" type="object"
                                string="Send PRO-FORMA Invoice" class="btn-primary"
                                groups="sale.group_proforma_sales"
                                invisible="pi_state != 'approved'"
                                context="{'proforma': True, 'validate_analytic': True}"/>
                        <button name="action_confirm_pi" string="Generate Sales Order" type="object"
                                confirm="are you sure you want to generate sales order?"
                                class="oe_highlight" invisible="pi_state != 'pi_sent' or so_from_pi_created"/>

<!--                        <button name="action_confirm" id="action_confirm" data-hotkey="q"-->
<!--                                confirm="Are you sure you want to confirm this proforma invoice?"-->
<!--                                string="Confirm" class="btn-primary" type="object" context="{'validate_analytic': True}"-->
<!--                                invisible="pi_state != 'pi_sent' or state == 'sale'"/>-->
                        <field name="pi_state" widget="statusbar" statusbar_visible="draft,in_review,approved,pi_sent"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_invoice"
                                    type="object"
                                    class="oe_stat_button"
                                    icon="fa-pencil-square-o"
                                    invisible="invoice_count == 0">
                                <field name="invoice_count" widget="statinfo" string="Invoices"/>
                            </button>
                            <button name="action_view_enquiry"
                                    type="object"
                                    class="oe_stat_button"
                                    icon="fa-pencil-square-o">
                                Enquiry
                            </button>
                        </div>
                        <div class="badge rounded-pill text-bg-secondary float-end fs-6 border-0"
                             invisible="not locked">
                            <i class="fa fa-lock"/>
                            Locked
                        </div>
                        <div class="oe_title">
                            <h1>
                                <field name="pi_ref_no" readonly="1"/>
                            </h1>
                        </div>
                        <group name="sale_header">
                            <group name="partner_details">
                                <field name="partner_id"
                                       widget="res_partner_many2one"
                                       context="{'res_partner_search_mode': 'customer'}"
                                       placeholder="Type to find a customer..." readonly="1"/>
                                <field name="contact_person_id"
                                       options="{'no_create': True, 'no_create_edit': True}"
                                       domain="[('parent_id','=',partner_id)]"
                                       required="1"
                                       readonly="1"
                                       context="{'default_type':'contact'}"/>
                                <field name="partner_invoice_id"
                                       groups="account.group_delivery_invoice_address"
                                       options="{'no_quick_create': True}"
                                       context="{'default_type':'invoice', 'show_address': False, 'show_vat': False, 'default_parent_id': partner_id}"
                                       readonly="1"/>
                                <field name="partner_shipping_id"
                                       groups="account.group_delivery_invoice_address"
                                       options="{'no_quick_create': True}"
                                       context="{'default_type':'delivery', 'show_address': False, 'show_vat': False, 'default_parent_id': partner_id}"
                                       readonly="1"/>
                            </group>
                            <group name="order_details">
<!--                                <field name="validity_date" invisible="state == 'sale'"-->
<!--                                       readonly="state in ['cancel', 'sale']"/>-->
<!--                                <div class="o_td_label" groups="base.group_no_one"-->
<!--                                     invisible="state in ['sale', 'cancel']">-->
<!--                                    <label for="date_order" string="Quotation Date"/>-->
<!--                                </div>-->
<!--                                <field name="date_order" nolabel="1" groups="base.group_no_one"-->
<!--                                       invisible="state in ['sale', 'cancel']" readonly="state in ['cancel', 'sale']"/>-->
<!--                                <div class="o_td_label" invisible="state in ['draft', 'sent']">-->
<!--                                    <label for="date_order" string="Order Date"/>-->
<!--                                </div>-->
<!--                                <field name="date_order" invisible="state in ['draft', 'sent']"-->
<!--                                       readonly="state in ['cancel', 'sale']" nolabel="1"/>-->
                                <field name="has_active_pricelist" invisible="1"/>
                                <field name="show_update_pricelist" invisible="1"/>
<!--                                <label for="pricelist_id"-->
<!--                                       groups="product.group_product_pricelist"-->
<!--                                       invisible="not has_active_pricelist"/>-->
<!--                                <div groups="product.group_product_pricelist"-->
<!--                                     class="o_row"-->
<!--                                     invisible="not has_active_pricelist">-->
<!--                                    <field name="pricelist_id" options="{'no_open':True,'no_create': True}"-->
<!--                                           readonly="state in ['cancel', 'sale']"/>-->
<!--                                    <button name="action_update_prices" type="object"-->
<!--                                            string=" Update Prices"-->
<!--                                            help="Recompute all prices based on this pricelist"-->
<!--                                            class="btn-link mb-1 px-0" icon="fa-refresh"-->
<!--                                            confirm="This will update the unit price of all products based on the new pricelist."-->
<!--                                            invisible="not show_update_pricelist or state in ['sale', 'cancel']"/>-->
<!--                                </div>-->
                                <field name="country_code" invisible="1"/>
                                <field name="company_id" invisible="1"/>
                                <field name="currency_id" invisible="1"/>
<!--                                <field name="pricelist_id" invisible="1" readonly="state in ['cancel', 'sale']"-->
<!--                                       groups="!product.group_product_pricelist"/>-->
                                <field name="pi_reject_reason_id" invisible="not pi_reject_reason_id" readonly="1"/>
                                <field name="pi_reject_feedback" invisible="not pi_reject_reason_id" readonly="1"/>
                                <field name="enquiry_id" readonly="1"/>
<!--                                <field name="name" readonly="1" string="Quote ID"/>-->
                                <field name="incoterm" readonly="1"/>
                                <field name="incoterm_location" readonly="1"/>
                                <field name="tax_country_id" invisible="1"/>
                                <field name="tax_calculation_rounding_method" invisible="1"/>
                                <field name="payment_term_id" placeholder="Immediate"
                                       options="{'no_open': True, 'no_create': True}" readonly="1"/>
                                <field name="customer_po_attachment" widget="many2many_binary" options="{'number_of_files': 1}" invisible="pi_state != 'pi_sent'"
                                       readonly="pi_state != 'pi_sent'"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Order Lines" name="order_lines">
                                <field
                                        name="order_line"
                                        widget="sol_o2m"
                                        mode="list,kanban"
                                        readonly="1">
                                    <form>
                                        <field name="display_type" invisible="1"/>
                                        <field name="is_downpayment" invisible="1"/>
                                        <!--
                                            We need the sequence field to be here for new lines to be added at the correct position.
                                            TODO: at some point we want to fix this in the framework so that an invisible field is not required.
                                        -->
                                        <field name="sequence" invisible="1"/>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <group>
                                            <group invisible="display_type">
                                                <field name="product_updatable" invisible="1"/>
                                                <div class="d-flex align-items-baseline">
                                                    <span class="fa fa-exclamation-triangle text-warning me-1"
                                                          title="This product is archived"
                                                          invisible="state not in ['draft', 'sent'] or not is_product_archived"
                                                    />
                                                    <field name="product_id"
                                                           domain="[('sale_ok', '=', True)]"
                                                           context="{
                                                    'partner_id': parent.partner_id,
                                                    'quantity': product_uom_qty,
                                                    'pricelist': parent.pricelist_id,
                                                    'uom': product_uom,
                                                    'company_id': parent.company_id,
                                                    'default_uom_id': product_uom,
                                                }"
                                                           readonly="not product_updatable"
                                                           required="not display_type and not is_downpayment"
                                                           force_save="1"
                                                           widget="many2one_barcode"
                                                    />
                                                </div>
                                                <field name="product_type" invisible="1"/>
                                                <field name="invoice_status" invisible="1"/>
                                                <field name="qty_to_invoice" invisible="1"/>
                                                <field name="qty_delivered_method" invisible="1"/>
                                                <field name="price_total" invisible="1"/>
                                                <field name="price_tax" invisible="1"/>
                                                <field name="price_subtotal" invisible="1"/>
                                                <field name="product_uom_readonly" invisible="1"/>
                                                <label for="product_uom_qty"/>
                                                <div class="o_row" name="ordered_qty">
                                                    <field
                                                            context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id, 'uom':product_uom, 'uom_qty_change':True, 'company_id': parent.company_id}"
                                                            name="product_uom_qty"/>
                                                    <field name="product_uom" invisible="1" groups="!uom.group_uom"/>
                                                    <field
                                                            name="product_uom"
                                                            force_save="1"
                                                            groups="uom.group_uom"
                                                            class="oe_no_button"
                                                            readonly="product_uom_readonly"
                                                            required="not display_type and not is_downpayment"/>
                                                </div>
                                                <label for="qty_delivered" string="Delivered"
                                                       invisible="parent.state != 'sale'"/>
                                                <div name="delivered_qty" invisible="parent.state != 'sale'">
                                                    <field name="qty_delivered"
                                                           readonly="qty_delivered_method != 'manual'"/>
                                                </div>
                                                <label for="qty_invoiced" string="Invoiced"
                                                       invisible="parent.state != 'sale'"/>
                                                <div name="invoiced_qty" invisible="parent.state != 'sale'">
                                                    <field name="qty_invoiced"/>
                                                </div>
                                                <field name="product_packaging_qty"
                                                       invisible="not product_id or not product_packaging_id"
                                                       groups="product.group_stock_packaging"/>
                                                <field name="product_packaging_id" invisible="not product_id"
                                                       context="{'default_product_id': product_id, 'list_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}"
                                                       groups="product.group_stock_packaging"/>
                                                <field name="price_unit"/>
                                                <field name="tax_id" widget="many2many_tags"
                                                       options="{'no_create': True}"
                                                       context="{'search_view_ref': 'account.account_tax_view_search'}"
                                                       domain="[('type_tax_use', '=', 'sale'), ('company_id', 'parent_of', parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                                       readonly="qty_invoiced &gt; 0"/>
                                                <t groups="sale.group_discount_per_so_line">
                                                    <label for="discount"/>
                                                    <div name="discount">
                                                        <field name="discount" class="oe_inline"/>
                                                        %
                                                    </div>
                                                </t>
                                                <!--
                                                    We need the sequence field to be here
                                                    because we want to be able to overwrite the default sequence value in the JS
                                                    in order for new lines to be added at the correct position.
                                                    NOTE: at some point we want to fix this in the framework so that an invisible field is not required.
                                                -->
                                                <field name="sequence" invisible="1"/>
                                            </group>
                                            <group invisible="display_type">
                                                <label for="customer_lead"/>
                                                <div name="lead">
                                                    <field name="customer_lead" class="oe_inline"/>
                                                    days
                                                </div>
                                                <field name="analytic_distribution" widget="analytic_distribution"
                                                       groups="analytic.group_analytic_accounting"
                                                       options="{'product_field': 'product_id', 'business_domain': 'sale_order'}"/>
                                            </group>
                                        </group>
                                        <label for="name" string="Description" invisible="display_type"/>
                                        <label for="name" string="Section Name (eg. Products, Services)"
                                               invisible="display_type != 'line_section'"/>
                                        <label for="name" string="Note" invisible="display_type != 'line_note'"/>
                                        <field name="name"/>
                                        <div name="invoice_lines" groups="base.group_no_one" invisible="display_type">
                                            <label for="invoice_lines"/>
                                            <field name="invoice_lines"/>
                                        </div>
                                        <field name="state" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                    </form>
                                    <list
                                            string="Sales Order Lines"
                                            editable="bottom"
                                            limit="200"
                                            edit="false"
                                            create="false"
                                            delete="false"
                                            decoration-warning="state in ['draft', 'sent'] and is_product_archived"
                                    >
                                        <control>
                                            <create name="add_product_control" string="Add a product"/>
                                            <create name="add_section_control" string="Add a section"
                                                    context="{'default_display_type': 'line_section'}"/>
                                            <create name="add_note_control" string="Add a note"
                                                    context="{'default_display_type': 'line_note'}"/>
                                            <button name="action_add_from_catalog" string="Catalog" type="object"
                                                    class="px-4 btn-link" context="{'order_id': parent.id}"/>
                                        </control>

                                        <field name="sequence_number"/>
                                        <!-- We do not display the type because we don't want the user to be bothered with that information if he has no section or note. -->
                                        <field name="display_type" column_invisible="True"/>
                                        <field name="product_uom_category_id" column_invisible="True"/>
                                        <field name="product_type" column_invisible="True"/>
                                        <field name="product_updatable" column_invisible="True"/>
                                        <field name="is_downpayment" column_invisible="True"/>
                                        <field name="part_code_id"/>
                                        <field
                                                name="product_id"
                                                string="Product Variant"
                                                readonly="not product_updatable"
                                                required="not display_type and not is_downpayment"
                                                force_save="1"
                                                context="{
                                        'partner_id': parent.partner_id,
                                        'quantity': product_uom_qty,
                                        'pricelist': parent.pricelist_id,
                                        'uom':product_uom,
                                        'company_id': parent.company_id,
                                        'default_lst_price': price_unit,
                                        'default_uom_id': product_uom,
                                    }"
                                                options="{
                                        'no_open': True,
                                    }"
                                                optional="hide"
                                                domain="[('sale_ok', '=', True)]"
                                                widget="sol_product_many2one"/>
                                        <field name="name"
                                               string="Description"
                                               readonly="1"/>
                                        <field name="product_template_id"
                                               string="Description"
                                               readonly="not product_updatable"
                                               required="not display_type and not is_downpayment"
                                               context="{
                                        'partner_id': parent.partner_id,
                                        'quantity': product_uom_qty,
                                        'pricelist': parent.pricelist_id,
                                        'uom':product_uom,
                                        'company_id': parent.company_id,
                                        'default_list_price': price_unit,
                                        'default_uom_id': product_uom,
                                    }"
                                               options="{
                                        'no_open': True,
                                    }"
                                               optional="show"
                                               domain="[('sale_ok', '=', True)]"
                                               widget="sol_product_many2one"
                                               placeholder="Type to find a product..." column_invisible="1"/>
                                        <field name="product_template_attribute_value_ids" column_invisible="1"/>
                                        <field name="product_custom_attribute_value_ids" column_invisible="1">
                                            <list>
                                                <field name="custom_product_template_attribute_value_id"/>
                                                <field name="custom_value"/>
                                            </list>
                                        </field>
                                        <field name="product_no_variant_attribute_value_ids" column_invisible="1"/>
                                        <field name="is_configurable_product" column_invisible="1"/>
                                        <field name="linked_line_id" column_invisible="1"/>
                                        <field name="virtual_id" column_invisible="1"/>
                                        <field name="linked_virtual_id" column_invisible="1"/>
                                        <field name="selected_combo_items" column_invisible="1"/>
                                        <field name="combo_item_id" column_invisible="1"/>
<!--                                        <field-->
<!--                                                name="name"-->
<!--                                                widget="sol_text"-->
<!--                                                optional="show"-->
<!--                                        />-->
                                        <field name="analytic_distribution" widget="analytic_distribution"
                                               optional="hide"
                                               groups="analytic.group_analytic_accounting"
                                               options="{'product_field': 'product_id', 'business_domain': 'sale_order', 'amount_field': 'price_subtotal'}"/>
                                        <field name="product_condition_id"/>
                                        <field name="brand_id"/>
                                        <field
                                                name="product_uom_qty"
                                                decoration-info="(not display_type and invoice_status == 'to invoice')"
                                                decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                                context="{
                                        'partner_id': parent.partner_id,
                                        'quantity': product_uom_qty,
                                        'pricelist': parent.pricelist_id,
                                        'uom': product_uom,
                                        'company_id': parent.company_id
                                    }"
                                                readonly="is_downpayment"/>
<!--                                        <field-->
<!--                                                name="qty_delivered"-->
<!--                                                decoration-info="(not display_type and invoice_status == 'to invoice')"-->
<!--                                                decoration-bf="(not display_type and invoice_status == 'to invoice')"-->
<!--                                                string="Delivered"-->
<!--                                                column_invisible="parent.state != 'sale'"-->
<!--                                                readonly="qty_delivered_method != 'manual' or is_downpayment"-->
<!--                                                optional="show"/>-->
<!--                                        <field name="qty_delivered_method" column_invisible="True"/>-->
<!--                                        <field-->
<!--                                                name="qty_invoiced"-->
<!--                                                decoration-info="(not display_type and invoice_status == 'to invoice')"-->
<!--                                                decoration-bf="(not display_type and invoice_status == 'to invoice')"-->
<!--                                                string="Invoiced"-->
<!--                                                column_invisible="parent.state != 'sale'"-->
<!--                                                optional="show"/>-->
<!--                                        <field name="qty_to_invoice" column_invisible="True"/>-->
                                        <field name="product_uom_readonly" column_invisible="True"/>
                                        <field name="reserve_stock_qty" readonly="1"/>
                                        <field name="available_qty" readonly="1"/>
                                        <field name="product_uom" column_invisible="True" groups="!uom.group_uom"/>
                                        <field
                                                name="product_uom"
                                                force_save="1"
                                                string="UoM"
                                                readonly="product_uom_readonly"
                                                required="not display_type and not is_downpayment"
                                                context="{'company_id': parent.company_id}"
                                                groups="uom.group_uom"
                                                options='{"no_open": True}'
                                                width="60px"
                                                optional="show"/>

<!--                                        <field-->
<!--                                                name="customer_lead"-->
<!--                                                optional="hide"-->
<!--                                                width="80px"-->
<!--                                                readonly="parent.state not in ['draft', 'sent', 'sale'] or is_downpayment"/>-->
<!--                                        <field name="product_packaging_qty"-->
<!--                                               invisible="not product_id or not product_packaging_id"-->
<!--                                               groups="product.group_stock_packaging" optional="show"/>-->
<!--                                        <field name="product_packaging_id" invisible="not product_id"-->
<!--                                               context="{'default_product_id': product_id, 'list_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}"-->
<!--                                               groups="product.group_stock_packaging" optional="show"/>-->
                                        <field name="lead_time_id"/>
                                        <field name="lead_stock_info" readonly="1"/>
                                        <field name="quoted_price"/>
                                        <field
                                                name="price_unit"
                                                readonly="qty_invoiced &gt; 0"/>
                                        <field name="technical_price_unit" column_invisible="1"/>
                                        <field
                                                name="tax_id"
                                                widget="many2many_tags"
                                                options="{'no_create': True}"
                                                domain="[('type_tax_use', '=', 'sale'), ('company_id', 'parent_of', parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                                context="{'active_test': True}"
                                                readonly="qty_invoiced &gt; 0 or is_downpayment"
                                                optional="show"/>
                                        <field
                                                name="discount"
                                                string="Disc.%"
                                                groups="sale.group_discount_per_so_line"
                                                width="50px"
                                                optional="show"/>
                                        <field name="is_downpayment" column_invisible="True"/>
                                        <field name="price_subtotal"
                                               column_invisible="parent.company_price_include == 'tax_included'"
                                               invisible="is_downpayment"
                                               string="Amount"/>
                                        <field name="price_total"
                                               column_invisible="parent.company_price_include == 'tax_excluded'"
                                               invisible="is_downpayment"
                                               string="Amount"/>
                                        <!-- Others fields -->
                                        <field name="tax_calculation_rounding_method" column_invisible="True"/>
                                        <field name="state" column_invisible="True"/>
                                        <field name="invoice_status" column_invisible="True"/>
                                        <field name="currency_id" column_invisible="True"/>
                                        <field name="price_tax" column_invisible="True"/>
                                        <field name="company_id" column_invisible="True"/>
                                    </list>
                                    <kanban class="o_kanban_mobile">
                                        <field name="price_subtotal"/>
                                        <field name="display_type"/>
                                        <field name="currency_id"/>
                                        <control>
                                            <create name="add_product_control" string="Add product"/>
                                            <create name="add_section_control" string="Add section"
                                                    context="{'default_display_type': 'line_section'}"/>
                                            <create name="add_note_control" string="Add note"
                                                    context="{'default_display_type': 'line_note'}"/>
                                            <button name="action_add_from_catalog"
                                                    context="{'order_id': parent.id}"
                                                    string="Catalog"
                                                    type="object"
                                                    class="btn-secondary"/>
                                        </control>
                                        <templates>
                                            <t t-name="card" class="row g-0 ps-0 pe-0">
                                                <t t-if="!record.display_type.raw_value">
                                                    <aside class="col-2 p-1">
                                                        <span t-att-title="record.product_id.value">
                                                            <field name="product_id" widget="image"
                                                                   options="{'preview_image': 'image_128', 'img_class': 'object-fit-contain w-100'}"/>
                                                        </span>
                                                    </aside>
                                                    <main class="col">
                                                        <div class="row">
                                                            <div class="col">
                                                                <span class="fa fa-exclamation-triangle text-warning me-1"
                                                                      title="This product is archived"
                                                                      invisible="state not in ['draft', 'sent'] or not is_product_archived"
                                                                />
                                                                <field name="product_id" class="fw-bold"/>
                                                            </div>
                                                            <div class="col-auto">
                                                                <field name="price_subtotal"
                                                                       class="fw-bolder float-end pe-1"
                                                                       widget="monetary"/>
                                                            </div>
                                                        </div>
                                                        <div class="text-muted">
                                                            Quantity:
                                                            <field name="product_uom_qty"/>
                                                            <field name="product_uom"/>
                                                        </div>
                                                        <div class="text-muted">
                                                            Unit Price:
                                                            <field name="price_unit"/>
                                                        </div>
                                                        <t t-if="record.discount?.raw_value">
                                                            <div class="text-muted">
                                                                Discount:
                                                                <field name="discount"/>%
                                                            </div>
                                                        </t>
                                                    </main>
                                                </t>
                                                <t t-if="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'">
                                                    <div t-attf-class="{{record.display_type.raw_value === 'line_section' ? 'fw-bold' : 'fst-italic' }}">
                                                        <field name="name"/>
                                                    </div>
                                                </t>
                                            </t>
                                        </templates>
                                    </kanban>
                                </field>
                                <div class="float-end d-flex gap-1 mb-2"
                                     name="so_button_below_order_lines">
                                    <button string="Discount"
                                            name="action_open_discount_wizard"
                                            type="object"
                                            class="btn btn-secondary"
                                            groups="sale.group_discount_per_so_line"/>
                                </div>
                                <group name="note_group" col="6" class="mt-2 mt-md-0">
                                    <group colspan="4" class="order-1 order-lg-0">
                                        <field colspan="2" name="note" nolabel="1"
                                               placeholder="Terms and conditions..."/>
                                    </group>
                                    <group class="oe_subtotal_footer d-flex order-0 order-lg-1 flex-column gap-0 gap-sm-3"
                                           colspan="2" name="sale_total">
                                        <field name="tax_totals" widget="account-tax-totals-field" nolabel="1"
                                               readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Terms &amp; Conditions" name="other_information">
                                <group>
                                    <group name="sales_person" string="Sales">
                                        <field name="user_id" widget="many2one_avatar_user" readonly="1"/>
                                        <field name="team_id"
                                               context="{'kanban_view_ref': 'sales_team.crm_team_view_kanban'}"
                                               options="{'no_create': True}" readonly="1"/>
<!--                                        <field name="company_id" options="{'no_create': True}"-->
<!--                                               groups="base.group_multi_company"/>-->
<!--                                        <field name="require_signature"-->
<!--                                               readonly="state in ['cancel', 'sale']"/>-->
<!--                                        <label for="require_payment"/>-->
<!--                                        <div id="require_payment">-->
<!--                                            <field name="require_payment"-->
<!--                                                   readonly="state in ['cancel', 'sale']"/>-->
<!--                                            <span class="mx-3" invisible="not require_payment">of</span>-->
<!--                                            <field name="prepayment_percent"-->
<!--                                                   readonly="state in ['cancel', 'sale']"-->
<!--                                                   invisible="not require_payment"-->
<!--                                                   widget="percentage"-->
<!--                                                   style="width: 3rem"/>-->
<!--                                        </div>-->
<!--                                        <field name="reference" readonly="1" invisible="not reference"/>-->
<!--                                        <field name="client_order_ref"/>-->
<!--                                        <field name="tag_ids" widget="many2many_tags"-->
<!--                                               options="{'color_field': 'color', 'no_create_edit': True}"/>-->
                                    </group>
<!--                                    <group name="sale_info" string="Invoicing">-->
<!--                                        <field name="show_update_fpos" invisible="1"/>-->
<!--                                        <label for="fiscal_position_id"/>-->
<!--                                        <div class="o_row">-->
<!--                                            <field name="fiscal_position_id" options="{'no_create': True}"/>-->
<!--                                            <button name="action_update_taxes" type="object"-->
<!--                                                    string=" Update Taxes"-->
<!--                                                    help="Recompute all taxes based on this fiscal position"-->
<!--                                                    class="btn-link mb-1 px-0" icon="fa-refresh"-->
<!--                                                    confirm="This will update all taxes based on the currently selected fiscal position."-->
<!--                                                    invisible="not show_update_fpos or state in ['sale', 'cancel']"/>-->
<!--                                        </div>-->
<!--                                        <field name="partner_invoice_id"-->
<!--                                               groups="!account.group_delivery_invoice_address" invisible="1"/>-->
<!--                                        <field name="journal_id" groups="base.group_no_one"-->
<!--                                               readonly="invoice_count != 0 and state == 'sale'"/>-->
<!--                                        <field name="invoice_status" invisible="state != 'sale'"-->
<!--                                               groups="base.group_no_one"/>-->
<!--                                        &lt;!&ndash; test_event_configurator &ndash;&gt;-->
<!--                                        <field name="invoice_status" invisible="1" groups="!base.group_no_one"/>-->
<!--                                    </group>-->
<!--                                    <group name="sale_shipping" string="Shipping">-->
<!--                                        <label for="commitment_date" string="Delivery Date"/>-->
<!--                                        <div name="commitment_date_div" class="o_row">-->
<!--                                            <field name="commitment_date" readonly="state == 'cancel' or locked"/>-->
<!--                                            <span name="expected_date_span" class="text-muted">Expected:-->
<!--                                                <field name="expected_date" class="oe_inline"/>-->
<!--                                            </span>-->
<!--                                        </div>-->
<!--                                    </group>-->
<!--                                    <group string="Tracking" name="sale_reporting">-->
<!--                                        <field name="origin"/>-->
<!--                                        <field name="campaign_id" options="{'create_name_field': 'title'}"/>-->
<!--                                        <field name="medium_id"/>-->
<!--                                        <field name="source_id"/>-->
<!--                                    </group>-->
                                </group>
                            </page>
<!--                            <page groups="base.group_no_one" string="Customer Signature" name="customer_signature"-->
<!--                                  invisible="not require_signature and not signed_by and not signature and not signed_on">-->
<!--                                <group>-->
<!--                                    <field name="signed_by" readonly="signature"/>-->
<!--                                    <field name="signed_on" readonly="signature"/>-->
<!--                                    <field name="signature" widget="image"/>-->
<!--                                </group>-->
<!--                            </page>-->
                            <page name='internal_notes' string="Notes">
                                <field name="note_quote" placeholder="Internal notes..."/>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <!-- Basic list view for proforma invoices -->
        <record id="view_proforma_invoice_list" model="ir.ui.view">
            <field name="name">sale.order.proforma.list</field>
            <field name="model">sale.order</field>
            <field name="arch" type="xml">
                <list string="Proforma Invoices" create="false">
                    <field name="pi_ref_no"/>
                    <field name="date_order" string="PI Date"/>
                    <field name="enquiry_id" string="RFQ No"/>
                    <field name="partner_id" string="Client Company Name"/>
                    <field name="amount_total" sum="Total Value" widget="monetary"/>
                    <field name="currency_id" column_invisible="1"/>
                    <field name="pi_state" widget="badge"
                           decoration-success="pi_state == 'approved'"
                           decoration-info="pi_state == 'draft'"
                           decoration-primary="pi_state == 'in_review'"
                           decoration-muted="pi_state == 'pi_sent'" string="PI Status"/>
                </list>
            </field>
        </record>

        <record id="view_order_tree_ni_pi" model="ir.ui.view">
            <field name="name">sale.order.list.ni.pi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_tree"/>
            <field name="arch" type="xml">
                <field name="date_order" position="after">
                    <field name="pi_ref_no" string="PI Ref"/>
                    <field name="create_date" string="PI Date"/>
                </field>
            </field>
        </record>

        <!-- Action for proforma invoices -->
        <record id="action_proforma_invoices" model="ir.actions.act_window">
            <field name="name">Proforma Invoices</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('pi_state', 'in', ['in_review','approved','pi_sent','rejected'])]</field>
            <field name="view_ids" eval="[(5, 0, 0),
                                          (0, 0, {'view_mode': 'list', 'view_id': ref('view_proforma_invoice_list')}),
                                          (0, 0, {'view_mode': 'form', 'view_id': ref('view_order_form_ni')})]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No proforma invoices found
                </p>
                <p>
                    Create proforma invoices from quotations to send to your customers.
                </p>
            </field>
        </record>

        <!-- Menu for proforma invoices -->
        <menuitem id="menu_proforma_invoices"
                  name="Proforma Invoices"
                  web_icon="ni_proforma_invoice_on_sale,static/description/icon.png"
                  sequence="32">
            <menuitem id="menu_proforma_invoices_list"
                      name="Proforma Invoices"
                      action="action_proforma_invoices"
                      sequence="1"/>
        </menuitem>

        <menuitem id="menu_proforma_invoices_list"
                  name="Proforma Invoices"
                  action="action_proforma_invoices"
                  parent="sale.sale_order_menu"
                  sequence="29"/>

    </data>
</odoo>