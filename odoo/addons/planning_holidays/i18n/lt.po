# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* planning_holidays
# 
# Translators:
# <PERSON><PERSON> <gail<PERSON>@vialaurea.lt>, 2024
# <PERSON><PERSON><PERSON> ViaLaurea <<EMAIL>>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:45+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: planning_holidays
#: model_terms:ir.ui.view,arch_db:planning_holidays.planning_slot_view_search_inherit_planning_holidays
msgid "Employees on Time Off"
msgstr ""

#. module: planning_holidays
#. odoo-python
#: code:addons/planning_holidays/models/planning_slot.py:0
msgid "Operation not supported"
msgstr "Operacija nepalaikoma"

#. module: planning_holidays
#: model:ir.model,name:planning_holidays.model_planning_slot
msgid "Planning Shift"
msgstr "Pamainų planavimas"

#. module: planning_holidays
#: model:ir.model,name:planning_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr ""

#. module: planning_holidays
#: model:ir.model,name:planning_holidays.model_resource_calendar
msgid "Resource Working Time"
msgstr "Ištekliaus darbo laikas"
