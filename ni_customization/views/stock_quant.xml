<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_stock_move_line_operation_tree_inherit" model="ir.ui.view">
        <field name="name">stock.move.line.operations.list.inherit</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree"/>
        <field name="arch" type="xml">
            <field name="quant_id" position="attributes">
                <attribute name="string">Lot/Serial No.</attribute>
                <attribute name="widget">custom_pick_from</attribute>
            </field>
        </field>
    </record>
    <record id="stock_quant_view_inventory_dashboard" model="ir.ui.view">
        <field name="name">stock.quant.view.inventory.dashboard</field>
        <field name="model">stock.quant</field>
        <field name="arch" type="xml">
            <list create="0" edit="0">
                <field name="part_code_id"/>
                <field name="product_id" string="Product Description"/>
                <field name="brand_id"/>
                <field name="barcode"/>
                <field name="lot_id"/>
                <field name="location_id"/>
                <field name="warehouse_id"/>
                <field name="inventory_quantity_auto_apply" string="On Hand Quantity"/>
                <field name="is_returnable"/>
                <field name="is_warranty_product"/>
<!--                <field name="is_licence_tracked"/>-->
<!--                <field name="is_amc_tracked"/>-->
                <field name="hs_code"/>
                <field name="volume"/>
                <field name="weight"/>
            </list>
        </field>
    </record>

    <record id="action_inventory_dashboard_view" model="ir.actions.act_window">
        <field name="name">Inventory Dashboard</field>
        <field name="res_model">stock.quant</field>
        <field name="view_mode">list</field>
        <field name="context">{'search_default_internal_loc': True}</field>
        <field name="view_ids"
               eval="[(5, 0, 0),
                      (0, 0, {'view_mode': 'list', 'view_id': ref('stock_quant_view_inventory_dashboard')})]"/>
    </record>

    <menuitem id="menu_inventory_dashboard" name="Inventory Dashboard" action="action_inventory_dashboard_view" sequence="1"/>

</odoo>