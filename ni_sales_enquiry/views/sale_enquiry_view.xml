<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="sale_enquiry_form_view" model="ir.ui.view">
            <field name="name">sale.enquiry.form</field>
            <field name="model">sale.enquiry</field>
            <field name="arch" type="xml">
                <form string="Sale Enquiry" duplicate="false" delete="false">
                    <header>
                        <button name="action_submit" id="btn_action_submit" type="object" string="Submit"
                                class="btn-primary" invisible="state != 'new'"
                                confirm="Are you sure you want to submit this enquiry?"
                        />
                        <button name="action_create_purchase_quote" id="btn_action_create_purchase_quotation" type="object"
                                string="Create Purchase Quote"
                                class="btn-primary"
                                invisible="state not in ['so_generated','sourcing', 'pending']"
                                groups="purchase.group_purchase_user,purchase.group_purchase_manager"/>
                        <button name="action_create_kit_source" id="btn_action_create_source" type="object"
                                string="Create Kit Source"
                                class="btn-primary"
                                confirm="Are you sure you want to create kit source?"
                                invisible="state not in ['sourcing','so_generated'] or not purchase_order_ids"
                                groups="purchase.group_purchase_user,purchase.group_purchase_manager"/>
<!--                        <button name="action_enquiry_lost" id="btn_action_enquiry_lost" type="object"-->
<!--                                string="Mark as Lost"-->
<!--                                class="btn-primary" invisible="state not in ['pending','quoted']"/>-->
                        <button name="action_executed" id="btn_action_executed" type="object"
                                string="Mark as Executed"
                                class="btn-primary" invisible="state != 'delivered'"/>
                        <field name="state" widget="statusbar"
                               statusbar_visible="new,pending,quoted,pi_shared,so_generated,sourcing,procurement,inbound,grn,outbound,delivery_note,delivered,executed,lost"
                               readonly="1"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" type="object"
                                    invisible="not purchase_order_ids"
                                    name="action_view_purchase_order" icon="fa-usd">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Purchase Quotes</span>
                                </div>
                            </button>
                            <button class="oe_stat_button" type="object"
                                    invisible="not kit_source_ids"
                                    name="action_view_kit_source" icon="fa-th-list">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Kit Source</span>
                                </div>
                            </button>
                        </div>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <group name="enquiry_header">
                            <group>
                                <field name="partner_id"
                                       widget="res_partner_many2one"
                                       context="{'res_partner_search_mode': 'customer', 'show_address': 1, 'show_vat': True , 'default_contact_type':'customer'}"
                                       placeholder="Type to find a customer..."
                                       options="{'no_quick_create': True}"
                                       domain="[('contact_type','=','customer'), ('is_company', '=', True)]"
                                       readonly="state != 'new'"
                                       required="1"/>
                                <field name="contact_person_id"
                                       options="{'no_create': True, 'no_create_edit': True}"
                                       domain="[('parent_id','=',partner_id)]"
                                       required="1"
                                       readonly="state != 'new'"
                                       context="{'default_type':'contact', 'show_address': False, 'show_vat': False, 'is_contact_person': True}"/>
                                <field name="partner_shipping_id"
                                       readonly="not partner_id or state != 'new'"
                                       domain="[('parent_id','=',partner_id),('type','=','delivery')]"
                                       context="{'default_type':'delivery', 'show_address': False, 'show_vat': False}"/>
                                <field name="contact_number"/>
                                <field name="email"/>
                                <field name="user_id" groups="base.group_erp_manager"
                                       options="{'no_open': True}"
                                       readonly="state != 'new'"/>
                                <field name="user_id" groups="!base.group_erp_manager" readonly="1"
                                       options="{'no_open': True}"/>
                                <field name="team_leader_id" options="{'no_open': True}"/>
                            </group>
                            <group>
                                <field name="rfq_type_id" required="1" groups="!ni_sales_enquiry.group_walkin_stores" readonly="state != 'new'"/>
                                <field name="rfq_type_id" required="1" groups="ni_sales_enquiry.group_walkin_stores" readonly="1"/>
                                <field name="sale_order_id" invisible="not sale_order_id" readonly="1"
                                       options="{'no_open': True}"/>
                                <field name="enquiry_date" readonly="1"/>
                                <field name="used_part_code_ids" widget="many2many_tags" readonly="1" invisible="1"/>
                                <field name="enquiry_lost_reason_id" invisible="not enquiry_lost_reason_id"
                                       readonly="1"/>
                                <field name="enquiry_lost_feedback" invisible="not enquiry_lost_reason_id"
                                       readonly="1"/>
                                <field name="incoterm_id" required="1" readonly="state != 'new'"/>
                                <field name="incoterm_location" required="1" readonly="state != 'new'"/>
                                <field name="show_update_currency_btn" invisible="1" force_save="1"/>
                                <field name="payment_term_id" required="1" readonly="state != 'new'"/>
                                <label for="currency_id"/>
                                <div class="o_row">
                                    <field name="currency_id" readonly="state != 'new'" force_save="1"
                                           options="{'no_open':True,'no_create': True}"/>
                                    <button name="action_update_prices" type="object"
                                            string="Update Prices"
                                            help="Recompute all prices based on this currency"
                                            class="btn-link mb-1 px-0" icon="fa-refresh"
                                            confirm="This will update the unit price of all products based on the new currency."
                                            invisible="1"/>
                                </div>
                            </group>
                        </group>
                        <notebook>
                            <page string="Enquiry Lines" name="enquiry_lines">
                                <field
                                        name="enquiry_line_ids"
                                        widget="sol_o2m"
                                        mode="list"
                                        readonly="state == 'lost'">
                                    <list string="Enquiry Lines" editable="bottom" limit="200"
                                          default_order="sequence_number asc"
                                          decoration-info="is_subproduct">
                                        <control>
                                            <create name="add_product_control" string="Add a product"/>
                                        </control>

                                        <field name="sequence_number" readonly="1"/>
                                        <field name="part_code_id" required="1"
                                               options="{'no_quick_create': True, 'no_create_edit': True, 'no_open': True, 'no_create': True}"
                                               readonly="parent.state != 'new'" domain="[('id', 'not in', parent.used_part_code_ids)]"/>
                                        <field name="product_id"
                                               string="Product Description"
                                               context="{
                                                    'partner_id': parent.partner_id,
                                                    'quantity': product_uom_qty,
                                                    'uom':product_uom,'company_id': parent.company_id,
                                                    'default_uom_id': %(uom.product_uom_unit)d,
                                                    }"
                                               options="{'no_quick_create':True, 'no_open': True}"
                                               optional="hide"
                                               class="description_cls"
                                               domain="[('sale_ok', '=', True)]"
                                               placeholder="Type to find a product..."
                                               readonly="1"
                                               force_save="1"/>
                                        <field name="product_condition_id" required="1"
                                               options="{'no_quick_create':True,'no_create_edit':True, 'no_open': True}"
                                               readonly="parent.state != 'new'"/>
                                        <field name="product_category_id" optional="hide"
                                               options="{'no_open': True , 'no_create': True}"
                                               readonly="1" force_save="1"/>
                                        <field name="brand_id" options="{'no_open': True}"
                                               readonly="1"/>
                                        <field name="product_uom_qty" context="{
                                                'partner_id': parent.partner_id,
                                                'quantity': product_uom_qty,
                                                'uom': product_uom,
                                                'company_id': parent.company_id
                                            }" readonly="parent.state != 'new'"/>
                                        <field name="target_price"
                                               widget="monetary"
                                               class="text-danger"
                                               column_invisible="parent.state not in ['pending','quoted_to_salesperson','quoted','pi_shared']"
                                               readonly="parent.state not in ['pending','quoted_to_salesperson','quoted','pi_shared']"/>
                                        <field name="reserve_stock_qty"
                                               readonly="parent.state not in ['pending'] or available_qty > 0"/>
                                        <field name="available_qty" readonly="1"/>
                                        <field name="in_stock_qty"/>
                                        <field name="product_uom"
                                               force_save="1"
                                               string="UoM"
                                               required="1"
                                               context="{'company_id': parent.company_id}"
                                               options='{"no_open": True}'
                                               width="60px"
                                               optional="show" readonly="parent.state != 'new'"/>
                                        <field name="currency_id" column_invisible="True"/>
                                        <field name="company_id" column_invisible="True"/>
                                    </list>
                                </field>
                            </page>
                            <page name='optional_prod' string="Optional Products">
                                <field name="enquiry_optional_product_ids">
                                    <list edit="false" create="false" delete="false" editable="bottom">
                                        <field name="related_prod_part_code_id" string="Main Part Code"/>
                                        <field name="part_code_id"/>
                                        <field name="product_id"/>
                                        <field name="product_condition_id"/>
                                        <field name="quantity"/>
                                        <field name="on_hand_qty"/>
                                        <field name="uom_id"/>
                                        <button name="button_add_to_enquiry"
                                                type="object"
                                                class="oe_link"
                                                icon="fa-shopping-cart"
                                                title="Add to Enquiry Lines"
                                                invisible="is_present or parent.state != 'new'"/>
                                    </list>
                                </field>
                            </page>
                            <page name='internal_notes' string="Notes">
                                <field name="notes" placeholder="Internal notes..."/>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <record id="sale_enquiry_list_view" model="ir.ui.view">
            <field name="name">sale.enquiry.list</field>
            <field name="model">sale.enquiry</field>
            <field name="arch" type="xml">
                <list string="Sales Enquiry" delete="0" duplicate="0">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="user_id" widget="many2one_avatar_user"/>
                    <field name="team_leader_id"/>
                    <field name="enquiry_date"/>
                    <field name="state"
                           decoration-success="state == 'delivered'"
                           decoration-info="state == 'so_generated'"
                           decoration-primary="state == 'pending'"
                           decoration-danger="state == 'lost'"
                           decoration-muted="state == 'sourcing'"
                           widget="badge"/>
                    <button name="action_view_line" type="object" string="View Lines" class="oe_highlight"/>
                </list>
            </field>
        </record>

        <record id="sale_enquiry_search_view" model="ir.ui.view">
            <field name="name">sale.enquiry.search</field>
            <field name="model">sale.enquiry</field>
            <field name="arch" type="xml">
                <search string="Search Enquiry">
                    <field name="name" string="RFQ ID"/>
                    <field name="partner_id" operator="child_of"/>
                    <field name="user_id"/>
                    <field name="team_leader_id"/>
                    <field name="enquiry_line_ids" string="Part Code" filter_domain="[('enquiry_line_ids.part_code_id', 'ilike', self)]"/>
                    <field name="enquiry_line_ids" string="Brand" filter_domain="[('enquiry_line_ids.brand_id', 'ilike', self)]"/>
                    <separator/>
                    <filter string="Creation Date" name="filter_creation_date" date="create_date"
                            default_period="month"/>
                    <filter string="Enquiry Date" name="filter_date_enquiry" date="enquiry_date"
                            default_period="month"/>
                    <group expand="0" string="Group By">
                        <filter string="Salesperson" name="salesperson" context="{'group_by':'user_id'}"/>
                        <filter string="Team Leader" name="saleschannel" context="{'group_by':'team_leader_id'}"/>
                        <filter name="state" string="Status" context="{'group_by': 'state'}"/>
                        <separator orientation="vertical"/>
                        <filter string="Creation Date" context="{'group_by':'create_date:month'}" name="month"/>
                        <separator/>
                    </group>
                </search>
            </field>
        </record>

        <record id="sale_enquiry_line_search_view" model="ir.ui.view">
            <field name="name">sale.enquiry.line.search</field>
            <field name="model">sale.enquiry.line</field>
            <field name="arch" type="xml">
                <search string="Search Enquiry Line">
                    <field name="part_code_id"/>
                    <field name="user_id"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Enquiry" name="enquiry_id" context="{'group_by':'enquiry_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="sale_enquiry_act_window" model="ir.actions.act_window">
            <field name="name">Sales Enquiry</field>
            <field name="res_model">sale.enquiry</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="sale_enquiry_search_view"/>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    There is no enquiry
                </p>
            </field>
        </record>

        <menuitem
                id="sale_enquiry_root"
                name="CRM"
                web_icon="ni_sales_enquiry,static/description/icon.png"
                sequence="25"/>

        <menuitem name="Sales Enquiry" id="sale_enquiry_menu" action="sale_enquiry_act_window"
                  parent="sale_enquiry_root"
                  sequence="2"/>

        <menuitem name="Customers" id="customer_menu" action="account.res_partner_action_customer"
                  parent="sale_enquiry_root"
                  sequence="4"/>

        <record id="sale_enquiry_line_list_view" model="ir.ui.view">
            <field name="name">sale.enquiry.line.list</field>
            <field name="model">sale.enquiry.line</field>
            <field name="arch" type="xml">
                <list string="Sales Enquiry Line" create="false" edit="false" decoration-info="is_subproduct"
                      default_order="sequence_number asc">
                    <field name="sequence_number"/>
                    <field name="part_code_id"/>
                    <field name="product_id" string="Product Description" class="description_cls"/>
                    <field name="brand_id"/>
                    <field name="product_condition_id"/>
                    <field name="product_uom_qty"/>
                    <field name="product_uom" string="UoM"/>
                    <field name="state"
                           decoration-info="state == 'pending'"
                           decoration-primary="state == 'quoted_to_salesperson'"
                           widget="badge"/>
                </list>
            </field>
        </record>

        <record id="sale_enquiry_line_list_view_purchase" model="ir.ui.view">
            <field name="name">sale.enquiry.line.list</field>
            <field name="model">sale.enquiry.line</field>
            <field name="arch" type="xml">
                <list string="RFQ Statements" create="0" delete="0" multi_edit="1">
                    <field name="enquiry_id" readonly="1" string="RFQ ID"/>
                    <field name="part_code_id" readonly="1" options='{"no_open": True}'/>
                    <field name="product_id" string="Product Description" options='{"no_open": True}'
                           class="description_cls" readonly="1" optional="show"/>
                    <field name="brand_id" readonly="1" options='{"no_open": True}'/>
                    <field name="product_condition_id" readonly="1" options='{"no_open": True}'/>
                    <field name="product_uom_qty" readonly="1" options='{"no_open": True}'/>
                    <field name="product_uom" string="UoM" readonly="1" options='{"no_open": True}'/>
                    <field name="state"
                           decoration-info="state == 'pending'"
                           decoration-primary="state == 'quoted_to_salesperson'"
                           widget="badge"/>
                    <field name="user_id" widget="many2one_avatar_user" optional="show"/>
                    <field name="team_leader_id" widget="many2one_avatar_user" optional="show"/>
                    <field name="procurement_user_id" widget="many2one_avatar_user"
                           groups="purchase.group_purchase_manager" force_save="1" optional="show"/>
                </list>
            </field>
        </record>

        <record id="sale_enquiry_line_act_window_purchase" model="ir.actions.act_window">
            <field name="name">RFQ Statements</field>
            <field name="res_model">sale.enquiry.line</field>
            <field name="view_mode">list</field>
            <field name="domain">[('state', '!=', 'new')]</field>
            <field name="search_view_id" ref="sale_enquiry_line_search_view"/>
            <field name="context">{'search_default_enquiry_id': 1}</field>
            <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'list', 'view_id': ref('sale_enquiry_line_list_view_purchase')})]"/>
        </record>

        <menuitem name="RFQ Statements" id="sale_enquiry_line_menu_purchase_rfq"
                  action="ni_sales_enquiry.sale_enquiry_line_act_window_purchase"
                  parent="sale_enquiry_root"
                  sequence="5"/>

        <record id="sale_enquiry_line_act_window_sales" model="ir.actions.act_window">
            <field name="name">RFQ Statement</field>
            <field name="res_model">sale.enquiry.line</field>
            <field name="view_mode">list</field>
            <field name="search_view_id" ref="sale_enquiry_line_search_view"/>
            <field name="context">{'search_default_enquiry_id': 1}</field>
            <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'list', 'view_id': ref('sale_enquiry_line_list_view_purchase')})]"/>
        </record>

        <record id="sale_enquiry_pending_to_quote_act_window" model="ir.actions.act_window">
            <field name="name">Pending For Quotes</field>
            <field name="res_model">sale.enquiry</field>
            <field name="view_mode">list,form</field>
            <field name="context">{'is_pending_quote': True}</field>
            <field name="domain" eval="False"/>
            <field name="search_view_id" ref="sale_enquiry_search_view"/>
        </record>

        <menuitem name="RFQ Statement" id="sale_enquiry_line_menu_sales"
                  action="ni_sales_enquiry.sale_enquiry_line_act_window_sales"
                  parent="sale_enquiry_root"
                  sequence="5"/>

        <menuitem name="Pending For Quotes" id="sale_enquiry_menu_pending_to_quote"
                  action="ni_sales_enquiry.sale_enquiry_pending_to_quote_act_window"
                  parent="sale_enquiry_root"
                  sequence="1"/>
    </data>
</odoo>