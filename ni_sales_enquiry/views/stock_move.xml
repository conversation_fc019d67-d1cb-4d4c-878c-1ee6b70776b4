<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_move_line_form_view_inherit" model="ir.ui.view">
        <field name="name">stock.move.line.form.view.inherit</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree"/>
        <field name="arch" type="xml">
<!--            <xpath expr="//field[@name='quant_id']" position="attributes">-->
<!--                <attribute name="domain">[('product_id', '=', product_id), ('location_id', 'child_of', picking_location_id),('product_condition_id', '=', product_condition_id)]</attribute>-->
<!--                <attribute name="column_invisible">1</attribute>-->
<!--            </xpath>-->
<!--            <xpath expr="//field[@name='lot_id']" position="attributes">-->
<!--                <attribute name="column_invisible">picking_type_code != 'outgoing'</attribute>-->
<!--                <attribute name="readonly">0</attribute>-->
<!--            </xpath>-->
<!--            <xpath expr="//field[@name='lot_name']" position="attributes">-->
<!--                <attribute name="column_invisible">picking_type_code != 'incoming'</attribute>-->
<!--                <attribute name="readonly">0</attribute>-->
<!--            </xpath>-->
            <xpath expr="//field[@name='lot_name']" position="after">
                <field name="product_condition_id" optional="show" options="{'no_open': True}" required="lot_name"/>
            </xpath>
        </field>
    </record>

    <record id="view_stock_move_line_detailed_operation_tree_inherit" model="ir.ui.view">
        <field name="name">stock.move.line.operations.list.inherit</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='quant_id']" position="attributes">
                <attribute name="domain">[('product_id', '=', product_id), ('location_id', 'child_of', picking_location_id),('product_condition_id', '=', product_condition_id)]</attribute>
            </xpath>
            <xpath expr="//field[@name='lot_id']" position="attributes">
                <attribute name="domain">[('product_condition_id', '=', product_condition_id), ('product_id', '=', product_id), ('company_id', 'in', [company_id, False])]</attribute>
            </xpath>
            <xpath expr="//field[@name='lot_name']" position="after">
                <field name="product_condition_id" optional="show" options="{'no_open': True}" required="lot_name"/>
            </xpath>
        </field>
    </record>

    <record id="view_picking_form_inherit" model="ir.ui.view">
        <field name="name">stock.picking.form.inherit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="product_condition_id" optional="show" options="{'no_open': True}"/>
            </xpath>
        </field>
    </record>
</odoo>