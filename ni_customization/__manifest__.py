# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.


{
    'name': 'NI-Customization',
    'version': '18.0',
    'category': 'Sales/CRM',
    'sequence': 45,
    'summary': 'NI Customization',
    'depends': ['base', 'ni_sales_enquiry', 'qz_list_view_expand', 'account', 'purchase_stock', 'sale_stock',
                'stock_delivery', 'base_vat', 'stock', 'spreadsheet_sale_management', 'stock_picking_batch'],
    'data': [
        'data/mmr_action.xml',
        'data/trade_expiry_notification_scheduler.xml',
        'data/dsr_data_scheduler.xml',
        'data/ir_sequence.xml',
        'data/product_data.xml',
        'data/quotation_template.xml',
        'data/expiry_notification_scheduler.xml',
        'security/security.xml',
        'security/ir.model.access.csv',
        'security/ir_rule.xml',
        'views/mmr_report.xml',
        'views/purchase_order.xml',
        'views/sale_order_views.xml',
        'views/res_partner.xml',
        'views/account_move_views.xml',
        'views/stock_picking_views.xml',
        'views/dsr_dsr.xml',
        'views/product_views.xml',
        'views/rfq_pfq.xml',
        'views/quote_pfp.xml',
        'views/pi_pfso.xml',
        'views/so_pfp.xml',
        'views/sale_enquiry_views.xml',
        'views/stock_quant.xml',
        'views/product_family_view.xml',
        'views/account_payment_view.xml',
        'views/po_reject_reason_view.xml',
        'views/res_config_settings.xml',
        'views/enquiry_quoted_price_history.xml',
        'views/rma_reason.xml',
        'wizards/po_reject_reason_wizard.xml',
        'wizards/renewal_wizard.xml',
        'wizards/quoted_price_filter_wizard_views.xml',
        'reports/price_history.xml',
        'reports/mmr_report.xml',
        'wizards/proforma_invoice_wizard.xml',
        'wizards/stock_return_picking.xml',
        'wizards/stock_picking_batch.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'ni_customization/static/src/widget/stock_pick_from_inherit.js',
        ],
    },
    'installable': True,
    'application': True,
    'license': 'LGPL-3',
}
